#!/usr/bin/env python3
"""
Health check test that writes results to a file.
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:5000"

def test_health_and_write_results():
    """Test health endpoints and write results to file."""
    
    results = []
    endpoints = ["/health", "/api/health", "/health-check"]
    
    results.append(f"🏥 HEALTH ENDPOINT TEST - {datetime.now()}")
    results.append("=" * 50)
    
    for endpoint in endpoints:
        results.append(f"\n🔍 Testing {endpoint}...")
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            
            if response.status_code == 200:
                results.append(f"✅ {endpoint} - STATUS: {response.status_code}")
                
                try:
                    data = response.json()
                    status = data.get('status', 'unknown')
                    results.append(f"   📊 Health Status: {status}")
                    
                    if 'timestamp' in data:
                        results.append(f"   🕒 Timestamp: {data['timestamp']}")
                    
                except json.JSONDecodeError:
                    results.append(f"   📝 Response: {response.text[:100]}...")
                    
            else:
                results.append(f"❌ {endpoint} - STATUS: {response.status_code}")
                results.append(f"   📝 Response: {response.text[:100]}...")
                
        except requests.exceptions.RequestException as e:
            results.append(f"❌ {endpoint} - ERROR: {e}")
    
    results.append(f"\n🎯 HEALTH TEST COMPLETE")
    
    # Write results to file
    with open("health_test_results.txt", "w") as f:
        f.write("\n".join(results))
    
    return len([r for r in results if "✅" in r]) > 0

if __name__ == "__main__":
    success = test_health_and_write_results()
    
    # Also test the main API endpoint
    try:
        test_data = {
            "session_id": "health_test_session",
            "student_id": "health_test_student", 
            "lesson_ref": "P5-COM-001",
            "content_to_enhance": "Test connection",
            "grade": "Primary 5",
            "subject": "Computing",
            "level": "5",
            "student_info": {"first_name": "HealthTest"},
            "chat_history": []
        }
        
        response = requests.post(f"{BASE_URL}/api/enhance-content", json=test_data, timeout=10)
        
        with open("api_test_results.txt", "w") as f:
            f.write(f"API Test Results - {datetime.now()}\n")
            f.write("=" * 40 + "\n")
            f.write(f"Status Code: {response.status_code}\n")
            f.write(f"Response Length: {len(response.text)}\n")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    phase = data.get('data', {}).get('current_phase', 'Unknown')
                    f.write(f"Current Phase: {phase}\n")
                    f.write("✅ API CONNECTION SUCCESS\n")
                except:
                    f.write("❌ JSON Parse Error\n")
            else:
                f.write(f"❌ API Error: {response.text[:200]}\n")
                
    except Exception as e:
        with open("api_test_results.txt", "w") as f:
            f.write(f"❌ API Test Exception: {e}\n")
