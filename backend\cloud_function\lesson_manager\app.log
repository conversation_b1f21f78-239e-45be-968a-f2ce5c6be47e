[2025-06-25 20:18:07,776] INFO - __main__ - main.py:617 - Logging configuration complete with immediate console output
[2025-06-25 20:18:07,821] INFO - __main__ - main.py:693 - INIT_INFO: Flask app instance created and CORS configured.
[2025-06-25 20:18:07,824] INFO - __main__ - main.py:931 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-06-25 20:18:07,992] INFO - __main__ - main.py:960 - Phase transition fixes imported successfully
[2025-06-25 20:18:08,033] INFO - __main__ - main.py:3268 - Successfully imported utils functions
[2025-06-25 20:18:08,074] INFO - __main__ - main.py:3276 - Successfully imported extract_ai_state functions
[2025-06-25 20:18:08,200] INFO - __main__ - main.py:3726 - FLASK: Using unified Firebase initialization approach...
[2025-06-25 20:18:08,261] INFO - unified_firebase_init - unified_firebase_init.py:90 - Attempting Firebase initialization with: firebase-adminsdk-service-key.json
[2025-06-25 20:18:09,849] INFO - unified_firebase_init - unified_firebase_init.py:95 - ✅ Firebase initialized successfully with: firebase-adminsdk-service-key.json
[2025-06-25 20:18:09,849] INFO - unified_firebase_init - unified_firebase_init.py:121 - Testing Firestore connectivity with lightweight operation...
[2025-06-25 20:18:10,882] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-06-25 20:18:10,893] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-06-25 20:18:11,424] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
[2025-06-25 20:18:12,206] INFO - unified_firebase_init - unified_firebase_init.py:165 - ✅ Firestore connected successfully - connectivity test passed
[2025-06-25 20:18:12,207] INFO - __main__ - main.py:3734 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-06-25 20:18:12,207] INFO - __main__ - main.py:3824 - Gemini API will be initialized on first use (lazy loading).
[2025-06-25 20:18:12,226] INFO - __main__ - main.py:1101 - Successfully imported timetable_generator functions
[2025-06-25 20:18:12,232] DEBUG - google.auth._default - _default.py:256 - Checking solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json for explicit credentials as part of auth process...
[2025-06-25 20:18:12,286] DEBUG - google.auth._default - _default.py:256 - Checking solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json for explicit credentials as part of auth process...
[2025-06-25 20:18:12,350] INFO - __main__ - main.py:16826 - Google Cloud Storage client initialized successfully.
[2025-06-25 20:18:12,430] INFO - __main__ - main.py:17718 - Starting Lesson Manager Service...
[2025-06-25 20:18:12,440] INFO - __main__ - main.py:17733 - Flask server starting on host 0.0.0.0, port 5000
[2025-06-25 20:18:12,441] INFO - __main__ - main.py:17734 - Debug mode: ON
[2025-06-25 20:18:12,524] INFO - werkzeug - _internal.py:97 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
[2025-06-25 20:18:12,524] INFO - werkzeug - _internal.py:97 - [33mPress CTRL+C to quit[0m
[2025-06-25 20:18:52,683] INFO - __main__ - main.py:5167 - REQUEST: POST http://localhost:5000/debug/enhance-content-test -> endpoint: debug_enhance_content_test
[2025-06-25 20:18:52,690] INFO - __main__ - main.py:5210 - Incoming request: {"request_id": "9cfe4379-5324-4a80-bd0e-174c8ec16521", "timestamp": "2025-06-25T19:18:52.688170+00:00", "method": "POST", "path": "/debug/enhance-content-test", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown", "body": {"session_id": "test", "message": "Start lesson"}}
[2025-06-25 20:18:52,699] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 20:18:52] "POST /debug/enhance-content-test HTTP/1.1" 200 -
[2025-06-25 20:19:25,323] INFO - __main__ - main.py:5167 - REQUEST: GET http://localhost:5000/ -> endpoint: None
[2025-06-25 20:19:25,325] INFO - __main__ - main.py:5210 - Incoming request: {"request_id": "d507a73f-bb57-40e2-b895-dfa76c337ad9", "timestamp": "2025-06-25T19:19:25.324997+00:00", "method": "GET", "path": "/", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown"}
[2025-06-25 20:19:25,340] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 20:19:25] "[33mGET / HTTP/1.1[0m" 404 -
[2025-06-25 20:19:27,414] INFO - __main__ - main.py:5167 - REQUEST: GET http://localhost:5000/ -> endpoint: None
[2025-06-25 20:19:27,414] INFO - __main__ - main.py:5210 - Incoming request: {"request_id": "55d1fc97-00d2-4946-9ef8-849e78425d71", "timestamp": "2025-06-25T19:19:27.414619+00:00", "method": "GET", "path": "/", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown"}
[2025-06-25 20:19:27,415] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 20:19:27] "[33mGET / HTTP/1.1[0m" 404 -
[2025-06-25 20:19:29,506] INFO - __main__ - main.py:5167 - REQUEST: POST http://localhost:5000/debug/quiz-test -> endpoint: debug_quiz_test
[2025-06-25 20:19:29,507] INFO - __main__ - main.py:5210 - Incoming request: {"request_id": "32752e18-5402-4468-aaf7-5eca5cdd8d8f", "timestamp": "2025-06-25T19:19:29.507143+00:00", "method": "POST", "path": "/debug/quiz-test", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown", "body": {"message": "test", "context": {}}}
[2025-06-25 20:19:29,534] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 20:19:29,578] INFO - __main__ - main.py:3815 - Gemini API configured successfully with models/gemini-2.5-flash-lite-preview-06-17 and safety filters disabled.
[2025-06-25 20:19:29,581] INFO - __main__ - main.py:13421 - [debug-test] QUIZ HANDLER ENTRY: Current Q Index: 0, Answers: 0, Total Questions: 0
[2025-06-25 20:19:29,582] ERROR - __main__ - main.py:13425 - [debug-test] CRITICAL: quiz_questions_generated is missing in context. Cannot proceed.
[2025-06-25 20:19:29,590] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 20:19:29] "POST /debug/quiz-test HTTP/1.1" 200 -
[2025-06-25 20:19:31,642] INFO - __main__ - main.py:5167 - REQUEST: POST http://localhost:5000/debug/enhance-content-test -> endpoint: debug_enhance_content_test
[2025-06-25 20:19:31,643] INFO - __main__ - main.py:5210 - Incoming request: {"request_id": "cbc47daf-754e-4964-8914-a25d2e874e09", "timestamp": "2025-06-25T19:19:31.643164+00:00", "method": "POST", "path": "/debug/enhance-content-test", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown", "body": {"session_id": "diag_test_1750879169", "message": "Start lesson"}}
[2025-06-25 20:19:31,645] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 20:19:31] "POST /debug/enhance-content-test HTTP/1.1" 200 -
[2025-06-25 20:19:33,707] INFO - __main__ - main.py:5167 - REQUEST: POST http://localhost:5000/api/enhance-content -> endpoint: enhance_content_api
[2025-06-25 20:19:33,709] INFO - __main__ - main.py:5210 - Incoming request: {"request_id": "3bbe56b1-9d3d-4856-a898-9ced3bc58fdb", "timestamp": "2025-06-25T19:19:33.708571+00:00", "method": "POST", "path": "/api/enhance-content", "ip": "127.0.0.1", "user_agent": "python-requests/2.32.3", "user_id": "anonymous", "role": "unknown", "body": {"session_id": "diag_test_1750879169", "student_id": "diag_test_student", "lesson_ref": "P5-COM-001", "content_to_enhance": "Start lesson", "grade": "Primary 5", "subject": "Computing", "level": "5", "student_info": {"first_name": "DiagTest"}, "chat_history": []}}
[2025-06-25 20:19:33,714] INFO - auth_decorator - auth_decorator.py:39 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb][require_auth] Decorator invoked for path: /api/enhance-content
[2025-06-25 20:19:33,718] INFO - auth_decorator - auth_decorator.py:56 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb][require_auth] Development mode detected - bypassing authentication
[2025-06-25 20:19:33,721] DEBUG - asyncio - proactor_events.py:631 - Using proactor: IocpProactor
[2025-06-25 20:19:33,728] WARNING - __main__ - main.py:5388 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-25 20:19:33,733] INFO - __main__ - main.py:5441 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Parsed Params: student_id='diag_test_student', session_id='diag_test_1750879169', lesson_ref='P5-COM-001'
[2025-06-25 20:19:34,347] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-06-25 20:19:34,359] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-06-25 20:19:35,079] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
[2025-06-25 20:19:36,716] WARNING - __main__ - main.py:4719 - Student profile not found for ID: diag_test_student
[2025-06-25 20:19:36,718] INFO - __main__ - main.py:5481 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 👤 Merged inline student info: {'first_name': 'DiagTest'}
[2025-06-25 20:19:36,720] INFO - __main__ - main.py:5487 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 👤 Final student profile: {'first_name': 'DiagTest', 'name': 'Student'}
[2025-06-25 20:19:36,723] INFO - __main__ - main.py:1236 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] fetch_lesson_data: Fetching lesson with parameters:
[2025-06-25 20:19:36,724] INFO - __main__ - main.py:1237 -   • Country: Nigeria
[2025-06-25 20:19:36,726] INFO - __main__ - main.py:1238 -   • Curriculum: National Curriculum
[2025-06-25 20:19:36,727] INFO - __main__ - main.py:1239 -   • Grade: Primary 5
[2025-06-25 20:19:36,727] INFO - __main__ - main.py:1240 -   • Level: 5
[2025-06-25 20:19:36,728] INFO - __main__ - main.py:1241 -   • Subject: Computing
[2025-06-25 20:19:36,729] INFO - __main__ - main.py:1242 -   • Lesson ID: P5-COM-001
[2025-06-25 20:19:36,730] INFO - __main__ - main.py:1261 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Attempting primary path: countries/Nigeria/curriculums/National Curriculum/grades/Primary 5/levels/5/subjects/Computing/lessonRef/P5-COM-001
[2025-06-25 20:19:37,005] WARNING - __main__ - main.py:1267 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Primary path failed, trying alternative: root_lessonRef_collection (lessonRef/P5-COM-001)
[2025-06-25 20:19:37,506] WARNING - __main__ - main.py:1267 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Primary path failed, trying alternative: legacy_lessons_collection (lessons/P5-COM-001)
[2025-06-25 20:19:37,859] INFO - __main__ - main.py:1325 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Successfully retrieved document with keys: ['additionalNotes', 'interactiveElements', 'metadata', 'lessonRef', 'lessonTitle', 'instructionalSteps', 'learningObjectives', 'extensionActivities', 'adaptiveStrategies', 'conclusion', 'quizzes', 'introduction']
[2025-06-25 20:19:37,861] INFO - __main__ - main.py:1476 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Extracted 10 key concepts: ['Design', 'write', 'debug', 'programs', 'solve']...
[2025-06-25 20:19:37,863] INFO - __main__ - main.py:1556 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Universal content extraction: 1376 characters from 3 steps
[2025-06-25 20:19:37,863] INFO - __main__ - main.py:1593 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Universal conversion: 3 steps → 3 sections
[2025-06-25 20:19:37,863] INFO - __main__ - main.py:1411 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Field mapping completed:
[2025-06-25 20:19:37,864] INFO - __main__ - main.py:1412 -   - Subject: Computing
[2025-06-25 20:19:37,864] INFO - __main__ - main.py:1413 -   - Topic: Advanced Coding Techniques: Variables and Functions
[2025-06-25 20:19:37,864] INFO - __main__ - main.py:1414 -   - Grade: Primary 5
[2025-06-25 20:19:37,864] INFO - __main__ - main.py:1415 -   - Key Concepts: 10 extracted
[2025-06-25 20:19:37,864] INFO - __main__ - main.py:1416 -   - Instructional Steps: 3
[2025-06-25 20:19:37,865] INFO - __main__ - main.py:1614 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅ Universal content structure recognized: instructionalSteps (3 steps)
[2025-06-25 20:19:37,865] INFO - __main__ - main.py:1629 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅ All required fields present after universal mapping
[2025-06-25 20:19:37,865] INFO - __main__ - main.py:1334 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Successfully mapped lesson fields for AI inference
[2025-06-25 20:19:37,865] DEBUG - __main__ - main.py:657 - Cached result for fetch_lesson_data
[2025-06-25 20:19:37,866] INFO - __main__ - main.py:5506 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅ All required fields present after lesson content parsing and mapping
[2025-06-25 20:19:37,867] INFO - __main__ - main.py:5545 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Attempting to infer module. GS Subject Slug: 'computing'.
[2025-06-25 20:19:37,868] INFO - __main__ - main.py:2391 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Inferring module for subject 'computing', lesson 'Advanced Coding Techniques: Variables and Functions'.
[2025-06-25 20:19:38,287] INFO - __main__ - main.py:2450 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Loaded metadata for module 'algorithms_and_computational_thinking' ('Algorithms & Computational Thinking')
[2025-06-25 20:19:38,289] INFO - __main__ - main.py:2450 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Loaded metadata for module 'computer_systems_and_networks' ('Computer Systems & Networks')
[2025-06-25 20:19:38,290] INFO - __main__ - main.py:2450 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Loaded metadata for module 'cyber_security' ('Cyber-Security')
[2025-06-25 20:19:38,291] INFO - __main__ - main.py:2450 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Loaded metadata for module 'data_representation_and_databases' ('Data Representation & Databases')
[2025-06-25 20:19:38,292] INFO - __main__ - main.py:2450 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Loaded metadata for module 'digital_literacy_and_e_safety' ('Digital Literacy & E-Safety')
[2025-06-25 20:19:38,293] INFO - __main__ - main.py:2450 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Loaded metadata for module 'programming_and_software_development' ('Programming & Software Development')
[2025-06-25 20:19:38,295] INFO - __main__ - main.py:2519 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Starting module inference for subject 'computing' with 6 module options
[2025-06-25 20:19:38,296] DEBUG - __main__ - main.py:2533 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Sample modules available (first 3):
- algorithms_and_computational_thinking: Algorithms & Computational Thinking
- computer_systems_and_networks: Computer Systems & Networks
- cyber_security: Cyber-Security
[2025-06-25 20:19:38,296] DEBUG - __main__ - main.py:2536 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Advanced Coding Techniques: Variables and Functions. Topic: Advanced Coding Techniques: Variables and Functions. Learning Objectives: Design, write, and debug programs to solve specific problems; Incorporate variables and functions in coding projects. Key Concepts: Design; write; deb...
[2025-06-25 20:19:38,298] DEBUG - __main__ - main.py:2537 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference Lesson Summary (first 300 chars): Lesson Title: Advanced Coding Techniques: Variables and Functions. Topic: Advanced Coding Techniques: Variables and Functions. Learning Objectives: Design, write, and debug programs to solve specific problems; Incorporate variables and functions in coding projects. Key Concepts: Design; write; debug...
[2025-06-25 20:19:38,298] DEBUG - __main__ - main.py:2538 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference Module Options (first 200 chars): 1. Slug: "algorithms_and_computational_thinking", Name: "Algorithms & Computational Thinking", Description: "UK strand: Computational Thinking; mirrors GCSE ‘Algorithmic Thinking’...."
2. Slug: "compu...
[2025-06-25 20:19:38,299] INFO - __main__ - main.py:2542 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Calling Gemini API for module inference...
[2025-06-25 20:19:39,467] DEBUG - __main__ - main.py:2314 - extract_gemini_text: Successfully extracted 36 characters
[2025-06-25 20:19:39,467] INFO - __main__ - main.py:2552 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Gemini API call completed in 1.17s. Raw response: 'programming_and_software_development'
[2025-06-25 20:19:39,468] DEBUG - __main__ - main.py:2574 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Cleaned slug: 'programming_and_software_development'
[2025-06-25 20:19:39,468] INFO - __main__ - main.py:2579 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI Inference: Successfully matched module by slug. Chosen: 'programming_and_software_development' (Programming & Software Development)
[2025-06-25 20:19:39,468] INFO - __main__ - main.py:5578 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Successfully inferred module ID via AI: programming_and_software_development
[2025-06-25 20:19:39,468] INFO - __main__ - main.py:2628 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] CACHE MISS or fetch: Getting GS levels for subject 'computing', module 'programming_and_software_development'.
[2025-06-25 20:19:39,733] INFO - __main__ - main.py:2651 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Fetched metadata for module: 'Programming & Software Development'
[2025-06-25 20:19:40,340] INFO - __main__ - main.py:2683 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Successfully fetched 10 levels for module 'programming_and_software_development'.
[2025-06-25 20:19:40,341] INFO - __main__ - main.py:5604 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Effective module name for prompt context: 'Programming & Software Development' (Module ID: programming_and_software_development)
[2025-06-25 20:19:40,622] INFO - __main__ - main.py:2109 - No prior student performance document found for Topic: computing_Primary 5_computing_programming_and_software_development
[2025-06-25 20:19:41,206] WARNING - __main__ - main.py:5628 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍 SESSION STATE DEBUG:
[2025-06-25 20:19:41,207] WARNING - __main__ - main.py:5629 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   - Session exists: False
[2025-06-25 20:19:41,207] WARNING - __main__ - main.py:5630 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   - Current phase: None
[2025-06-25 20:19:41,207] WARNING - __main__ - main.py:5631 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   - State data keys: []
[2025-06-25 20:19:41,208] WARNING - __main__ - main.py:5651 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔒 STATE PROTECTION: phase='None', diagnostic_done=False, level=None
[2025-06-25 20:19:41,209] INFO - __main__ - main.py:5687 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] State protection not triggered
[2025-06-25 20:19:41,209] INFO - __main__ - main.py:5722 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-25 20:19:41,209] INFO - __main__ - main.py:5723 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   assigned_level_for_teaching (session): None
[2025-06-25 20:19:41,210] INFO - __main__ - main.py:5724 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   latest_assessed_level (profile): None
[2025-06-25 20:19:41,210] INFO - __main__ - main.py:5725 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   teaching_level_for_returning_student: None
[2025-06-25 20:19:41,211] INFO - __main__ - main.py:5726 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   has_completed_diagnostic_before: False
[2025-06-25 20:19:41,211] INFO - __main__ - main.py:5727 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   is_first_encounter_for_module: True
[2025-06-25 20:19:41,211] WARNING - __main__ - main.py:5732 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-25 20:19:41,212] INFO - __main__ - main.py:5738 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍 PHASE INVESTIGATION:
[2025-06-25 20:19:41,212] INFO - __main__ - main.py:5739 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Retrieved from Firestore: 'None'
[2025-06-25 20:19:41,213] INFO - __main__ - main.py:5740 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-25 20:19:41,213] INFO - __main__ - main.py:5741 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Is first encounter: True
[2025-06-25 20:19:41,214] INFO - __main__ - main.py:5742 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Diagnostic completed: False
[2025-06-25 20:19:41,214] INFO - __main__ - main.py:5755 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] � No stored phase found, starting with: 'diagnostic_start_probe'
[2025-06-25 20:19:41,214] INFO - __main__ - main.py:5762 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-25 20:19:41,215] INFO - __main__ - main.py:5764 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Final phase for AI logic: diagnostic_start_probe
[2025-06-25 20:19:41,215] WARNING - __main__ - main.py:2088 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] State key 'current_probing_level_number' had non-numeric value 'None', using default 5.
[2025-06-25 20:19:41,216] INFO - __main__ - main.py:5784 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] NEW SESSION: Forcing question_index to 0 (was: N/A)
[2025-06-25 20:19:41,216] INFO - __main__ - main.py:3889 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Diagnostic context validation passed
[2025-06-25 20:19:41,216] INFO - __main__ - main.py:3916 - DETERMINE_PHASE: New session or first interaction. Starting with diagnostic_start_probe.
[2025-06-25 20:19:41,217] WARNING - __main__ - main.py:5872 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'diagnostic_start_probe' for first encounter
[2025-06-25 20:19:41,217] INFO - __main__ - main.py:3999 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Enhanced diagnostic context with 45 fields
[2025-06-25 20:19:41,217] INFO - __main__ - main.py:5891 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Enhanced context with diagnostic information for phase: diagnostic_start_probe
[2025-06-25 20:19:41,218] INFO - __main__ - main.py:5904 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Robust context prepared successfully. Phase: diagnostic_start_probe
[2025-06-25 20:19:41,218] DEBUG - __main__ - main.py:5905 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'diagnostic_context', 'performance_tracking', 'level_specific_guidance', 'current_phase_for_ai']
[2025-06-25 20:19:41,218] WARNING - __main__ - main.py:6106 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🤖 NATURAL AI RESPONSE GENERATION:
[2025-06-25 20:19:41,219] WARNING - __main__ - main.py:6107 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🤖   - Current phase: diagnostic_start_probe
[2025-06-25 20:19:41,219] WARNING - __main__ - main.py:6108 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🤖   - Student query: Start lesson...
[2025-06-25 20:19:41,220] ERROR - __main__ - main.py:10739 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ❌ Error in generate_natural_ai_response: 'python_calculated_new_phase_for_block'
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\main.py", line 10704, in generate_natural_ai_response
    formatted_rules = BASE_INSTRUCTOR_RULES.format(**context)
KeyError: 'python_calculated_new_phase_for_block'
[2025-06-25 20:19:41,231] WARNING - __main__ - main.py:6130 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🤖 AI RESPONSE RECEIVED:
[2025-06-25 20:19:41,231] WARNING - __main__ - main.py:6131 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🤖   - Content length: 126 chars
[2025-06-25 20:19:41,231] WARNING - __main__ - main.py:6132 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🤖   - State updates: {}
[2025-06-25 20:19:41,232] WARNING - __main__ - main.py:6133 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🤖   - Raw state block: None...
[2025-06-25 20:19:41,232] ERROR - __main__ - main.py:6263 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI processing error: calculate_next_mandatory_phase() takes 2 positional arguments but 3 were given
[2025-06-25 20:19:41,232] INFO - __main__ - main.py:6269 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI processing completed in 0.01s
[2025-06-25 20:19:41,233] WARNING - __main__ - main.py:6280 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍 STATE UPDATE VALIDATION: current_phase='diagnostic_start_probe', new_phase='diagnostic_start_probe'
[2025-06-25 20:19:41,233] INFO - __main__ - main.py:4094 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 20:19:41,233] WARNING - __main__ - main.py:6289 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-25 20:19:41,234] WARNING - __main__ - main.py:6296 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] � PHASE MAINTAINED: diagnostic_start_probe
[2025-06-25 20:19:41,234] WARNING - __main__ - main.py:6303 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 20:19:41,234] WARNING - __main__ - main.py:6304 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 20:19:41,235] WARNING - __main__ - main.py:6305 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-25 20:19:41,235] WARNING - __main__ - main.py:6306 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   3. AI state updates: {'new_phase': 'diagnostic_start_probe'}
[2025-06-25 20:19:41,235] WARNING - __main__ - main.py:6307 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   4. Final phase to save: 'diagnostic_start_probe'
[2025-06-25 20:19:41,236] WARNING - __main__ - main.py:6310 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 💾 FINAL STATE APPLICATION:
[2025-06-25 20:19:41,236] WARNING - __main__ - main.py:6311 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 💾   - Current phase input: 'diagnostic_start_probe'
[2025-06-25 20:19:41,236] WARNING - __main__ - main.py:6312 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 💾   - State updates from AI: {'new_phase': 'diagnostic_start_probe'}
[2025-06-25 20:19:41,237] WARNING - __main__ - main.py:6313 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 20:19:41,237] WARNING - __main__ - main.py:6314 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 💾   - Phase change: False
[2025-06-25 20:19:41,238] INFO - __main__ - main.py:4126 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] DIAGNOSTIC_FLOW_METRICS:
[2025-06-25 20:19:41,238] INFO - __main__ - main.py:4127 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Phase transition: diagnostic_start_probe -> diagnostic_start_probe
[2025-06-25 20:19:41,238] INFO - __main__ - main.py:4128 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Current level: 5
[2025-06-25 20:19:41,238] INFO - __main__ - main.py:4129 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Question index: 0
[2025-06-25 20:19:41,239] INFO - __main__ - main.py:4130 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   First encounter: True
[2025-06-25 20:19:41,239] INFO - __main__ - main.py:4135 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Answers collected: 0
[2025-06-25 20:19:41,239] INFO - __main__ - main.py:4136 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Levels failed: 0
[2025-06-25 20:19:41,240] INFO - __main__ - main.py:4094 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI state update validation passed: diagnostic_start_probe → diagnostic_start_probe
[2025-06-25 20:19:41,240] INFO - __main__ - main.py:4140 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   State update valid: True
[2025-06-25 20:19:41,240] INFO - __main__ - main.py:4147 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb]   Diagnostic complete: False
[2025-06-25 20:19:41,241] INFO - __main__ - main.py:6357 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅ Phase Match: Python-calculated and AI-proposed phases are the same ('diagnostic_start_probe').
[2025-06-25 20:19:41,241] WARNING - __main__ - main.py:6365 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-25 20:19:41,242] WARNING - __main__ - main.py:6366 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   1. Input phase: 'diagnostic_start_probe'
[2025-06-25 20:19:41,242] WARNING - __main__ - main.py:6367 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   2. Python calculated next phase: 'diagnostic_start_probe'
[2025-06-25 20:19:41,243] WARNING - __main__ - main.py:6368 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   3. AI proposed phase: 'diagnostic_start_probe'
[2025-06-25 20:19:41,243] WARNING - __main__ - main.py:6369 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍   4. Final phase to save (after enforcement): 'diagnostic_start_probe'
[2025-06-25 20:19:41,243] WARNING - __main__ - main.py:6372 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 💾 FINAL STATE APPLICATION:
[2025-06-25 20:19:41,243] WARNING - __main__ - main.py:6373 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 💾   - Final phase to save: 'diagnostic_start_probe'
[2025-06-25 20:19:41,244] WARNING - __main__ - main.py:6383 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
[2025-06-25 20:19:41,244] INFO - __main__ - main.py:6392 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-25 20:19:41,244] INFO - __main__ - main.py:6393 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] 🔍 DEBUG original teaching_interactions: 0
[2025-06-25 20:19:41,563] WARNING - __main__ - main.py:6438 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-25 20:19:41,564] WARNING - __main__ - main.py:6439 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅   - Phase: diagnostic_start_probe
[2025-06-25 20:19:41,564] WARNING - __main__ - main.py:6440 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅   - Probing Level: 5
[2025-06-25 20:19:41,564] WARNING - __main__ - main.py:6441 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅   - Question Index: 0
[2025-06-25 20:19:41,565] WARNING - __main__ - main.py:6442 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅   - Diagnostic Complete: False
[2025-06-25 20:19:41,565] WARNING - __main__ - main.py:6449 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅   - Quiz Questions Saved: 0
[2025-06-25 20:19:41,565] WARNING - __main__ - main.py:6450 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅   - Quiz Answers Saved: 0
[2025-06-25 20:19:41,566] WARNING - __main__ - main.py:6451 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] ✅   - Quiz Started: False
[2025-06-25 20:19:42,375] INFO - __main__ - main.py:6493 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Created new session document: diag_test_1750879169
[2025-06-25 20:19:42,376] INFO - __main__ - main.py:11508 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-25 20:19:42,377] DEBUG - __main__ - main.py:2871 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-25 20:19:42,377] DEBUG - __main__ - main.py:6561 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] No final assessment data found in AI response
[2025-06-25 20:19:42,379] INFO - __main__ - main.py:6647 - [3bbe56b1-9d3d-4856-a898-9ced3bc58fdb] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-25 20:19:42,380] WARNING - __main__ - main.py:680 - High response time detected: 8.66s for enhance_content_api
[2025-06-25 20:19:42,380] INFO - werkzeug - _internal.py:97 - 127.0.0.1 - - [25/Jun/2025 20:19:42] "POST /api/enhance-content HTTP/1.1" 200 -
