[2025-06-29 23:19:11,433] INFO - main - main.py:695 - ================================================================================
[2025-06-29 23:19:11,434] INFO - main - main.py:696 - LESSON MANAGER BACKEND STARTING UP
[2025-06-29 23:19:11,435] INFO - main - main.py:697 - ================================================================================
[2025-06-29 23:19:11,435] INFO - main - main.py:698 - Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
[2025-06-29 23:19:11,435] INFO - main - main.py:699 - Current working directory: C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager
[2025-06-29 23:19:11,435] INFO - main - main.py:700 - Log level: DEBUG
[2025-06-29 23:19:11,436] INFO - main - main.py:701 - ================================================================================
[2025-06-29 23:19:11,436] INFO - main - main.py:703 - Logging configuration complete with immediate console output
[2025-06-29 23:19:11,436] INFO - main - main.py:704 - LOG SETUP COMPLETE - Console output should now be visible
[2025-06-29 23:19:11,440] INFO - main - main.py:779 - INIT_INFO: Flask app instance created and CORS configured.
[2025-06-29 23:19:11,444] INFO - main - main.py:958 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-06-29 23:19:11,444] INFO - main - main.py:987 - Phase transition fixes imported successfully
[2025-06-29 23:19:11,452] INFO - main - main.py:3270 - Successfully imported utils functions
[2025-06-29 23:19:11,452] INFO - main - main.py:3278 - Successfully imported extract_ai_state functions
[2025-06-29 23:19:11,458] INFO - main - main.py:3728 - FLASK: Using unified Firebase initialization approach...
[2025-06-29 23:19:11,458] INFO - unified_firebase_init - unified_firebase_init.py:65 - Firebase already initialized
[2025-06-29 23:19:11,458] INFO - main - main.py:3736 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-06-29 23:19:11,458] INFO - main - main.py:3826 - Gemini API will be initialized on first use (lazy loading).
[2025-06-29 23:19:11,485] INFO - main - main.py:1128 - Successfully imported timetable_generator functions
[2025-06-29 23:19:11,493] WARNING - auth_decorator - auth_decorator.py:56 - Could not fetch student name from Firestore: View function mapping is overwriting an existing endpoint function: debug_quiz_test
[2025-06-29 23:19:11,493] INFO - auth_decorator - auth_decorator.py:160 - 🔒 DEVELOPMENT: Using student_id=andrea_ugono_33305, name=Development Student
[2025-06-29 23:19:11,494] INFO - auth_decorator - auth_decorator.py:164 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a][require_auth] Development auth: uid=andrea_ugono_33305, name=Development Student
[2025-06-29 23:19:11,494] INFO - __main__ - main.py:5274 - 
================================================================================
[2025-06-29 23:19:11,495] WARNING - __main__ - main.py:5274 - 🔥🔥🔥 /api/enhance-content ENDPOINT HIT! 🔥🔥🔥
[2025-06-29 23:19:11,496] WARNING - __main__ - main.py:5274 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🚀🚀🚀 ENHANCE-CONTENT START 🚀🚀🚀
[2025-06-29 23:19:11,496] INFO - __main__ - main.py:5274 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍 RAW BODY: {"student_id":"andrea_ugono_33305","lesson_ref":"P5-COM-001","content_to_enhance":"Yes! Let's wrap it up!","country":"Nigeria","curriculum":"National Curriculum","grade":"primary-5","level":"P5","subject":"Computing","session_id":"session_0b03d1a2-9188-4ede-960f-5f7c8601eba4","chat_history":[{"role":"user","content":"It means learning how to use special tools in coding to make programs that can do more things and remember information.","timestamp":"2025-06-29T22:16:17.614Z"},{"role":"assistant",...
[2025-06-29 23:19:11,497] INFO - __main__ - main.py:5274 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍 PARSED JSON: {'student_id': 'andrea_ugono_33305', 'lesson_ref': 'P5-COM-001', 'content_to_enhance': "Yes! Let's wrap it up!", 'country': 'Nigeria', 'curriculum': 'National Curriculum', 'grade': 'primary-5', 'level': 'P5', 'subject': 'Computing', 'session_id': 'session_0b03d1a2-9188-4ede-960f-5f7c8601eba4', 'chat_history': [{'role': 'user', 'content': 'It means learning how to use special tools in coding to make programs that can do more things and remember information.', 'timestamp': '2025-06-29T22:16:17.614Z'}, {'role': 'assistant', 'content': '**Question 9 of 10 (Short Answer)**\n\n*Why is it helpful to learn about Advanced Coding Techniques: Variables and Functions in Computing?*', 'timestamp': '2025-06-29T22:16:39.209Z'}, {'role': 'user', 'content': 'It helps me build cooler and smarter programs that can do more things, like remembering my score or making characters move in different ways.', 'timestamp': '2025-06-29T22:17:18.363Z'}, {'role': 'assistant', 'content': '**Question 10 of 10 (Short Answer)**\n\n*Give an example of how Advanced Coding Techniques: Variables and Functions might be used in real life.*', 'timestamp': '2025-06-29T22:17:35.796Z'}, {'role': 'user', 'content': "In a video game, a variable could store how many coins I've collected, and a function could be used to add coins to my score every time I find them.", 'timestamp': '2025-06-29T22:18:09.205Z'}, {'role': 'assistant', 'content': "🎉 Excellent work, Andrea! You've completed the quiz. Let me compile your results.", 'timestamp': '2025-06-29T22:18:17.126Z'}, {'role': 'user', 'content': 'Ok', 'timestamp': '2025-06-29T22:18:21.972Z'}, {'role': 'assistant', 'content': "🎯 **Quiz Results for Advanced Coding Techniques: Variables and Functions**\n\nOutstanding! 🌟 Student! Here's your detailed performance analysis:\n\n📊 **Your Performance:**\n• Questions Answered: 10/10 ✅\n• Correct Answers: 10/10 ✅\n• Score: 100% - Outstanding! 🎯\n\n📋 Question Type Analysis:\n• Multiple Choice (Q1-4): 4/4 correct\n• Fill-in-the-blank (Q5-7): 3/3 correct  \n• Short Answer (Q8-10): 3/3 correct\n\n✨ **Areas of Strength:**\n• Strong grasp of fundamental concepts\n• Good analytical thinking skills\n• Clear communication of ideas\n\n📚 **Areas for Growth:**\n• Continue building on your solid foundation\n\n🧠 Key Concepts Reviewed:\n• Design\n• write\n• debug\n\n🎉 **You've mastered this topic exceptionally well!**\n\nYou've successfully completed the quiz portion of this Computing lesson on Advanced Coding Techniques: Variables and Functions!\n\nReady to wrap up with a comprehensive summary of everything you've learned today?", 'timestamp': '2025-06-29T22:18:35.303Z'}]}
[2025-06-29 23:19:11,498] INFO - __main__ - main.py:5274 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍  - Session ID from payload: session_0b03d1a2-9188-4ede-960f-5f7c8601eba4
[2025-06-29 23:19:11,498] INFO - __main__ - main.py:5274 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍  - Student ID from payload: andrea_ugono_33305
[2025-06-29 23:19:11,498] INFO - __main__ - main.py:5274 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍  - Lesson Ref from payload: P5-COM-001
[2025-06-29 23:19:11,498] DEBUG - __main__ - main.py:5331 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 📝 Parsed Params: student_id='andrea_ugono_33305', session_id='session_0b03d1a2-9188-4ede-960f-5f7c8601eba4', lesson_ref='P5-COM-001'
[2025-06-29 23:19:11,498] INFO - __main__ - main.py:5332 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Parsed Params: student_id='andrea_ugono_33305', session_id='session_0b03d1a2-9188-4ede-960f-5f7c8601eba4', lesson_ref='P5-COM-001'
[2025-06-29 23:19:12,281] INFO - __main__ - main.py:4623 - Processed student name for andrea_ugono_33305: first_name='Andrea', full_name='Andrea Ugono'
[2025-06-29 23:19:12,282] DEBUG - __main__ - main.py:737 - Cache hit for fetch_lesson_data
[2025-06-29 23:19:12,282] INFO - __main__ - main.py:5382 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ All required fields present after lesson content parsing and mapping
[2025-06-29 23:19:12,282] INFO - __main__ - main.py:5421 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Attempting to infer module. GS Subject Slug: 'computing'.
[2025-06-29 23:19:12,283] INFO - __main__ - main.py:2418 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Inferring module for subject 'computing', lesson 'Advanced Coding Techniques: Variables and Functions'.
[2025-06-29 23:19:13,092] INFO - __main__ - main.py:2477 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Loaded metadata for module 'algorithms_and_computational_thinking' ('Algorithms & Computational Thinking')
[2025-06-29 23:19:13,093] INFO - __main__ - main.py:2477 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Loaded metadata for module 'computer_systems_and_networks' ('Computer Systems & Networks')
[2025-06-29 23:19:13,093] INFO - __main__ - main.py:2477 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Loaded metadata for module 'cyber_security' ('Cyber-Security')
[2025-06-29 23:19:13,093] INFO - __main__ - main.py:2477 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Loaded metadata for module 'data_representation_and_databases' ('Data Representation & Databases')
[2025-06-29 23:19:13,093] INFO - __main__ - main.py:2477 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Loaded metadata for module 'digital_literacy_and_e_safety' ('Digital Literacy & E-Safety')
[2025-06-29 23:19:13,093] INFO - __main__ - main.py:2477 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Loaded metadata for module 'programming_and_software_development' ('Programming & Software Development')
[2025-06-29 23:19:13,094] INFO - __main__ - main.py:2546 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Starting module inference for subject 'computing' with 6 module options
[2025-06-29 23:19:13,094] DEBUG - __main__ - main.py:2560 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Sample modules available (first 3):
- algorithms_and_computational_thinking: Algorithms & Computational Thinking
- computer_systems_and_networks: Computer Systems & Networks
- cyber_security: Cyber-Security
[2025-06-29 23:19:13,094] DEBUG - __main__ - main.py:2563 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference Prompt (first 500 chars): 
    You are an expert curriculum mapping assistant.
    Your task is to identify the single most relevant Gold Standard Curriculum module for the given lesson content.

    Lesson Content Summary:
    Lesson Title: Advanced Coding Techniques: Variables and Functions. Topic: Advanced Coding Techniques: Variables and Functions. Learning Objectives: Design, write, and debug programs to solve specific problems; Incorporate variables and functions in coding projects. Key Concepts: Design; write; deb...
[2025-06-29 23:19:13,094] DEBUG - __main__ - main.py:2564 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference Lesson Summary (first 300 chars): Lesson Title: Advanced Coding Techniques: Variables and Functions. Topic: Advanced Coding Techniques: Variables and Functions. Learning Objectives: Design, write, and debug programs to solve specific problems; Incorporate variables and functions in coding projects. Key Concepts: Design; write; debug...
[2025-06-29 23:19:13,095] DEBUG - __main__ - main.py:2565 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference Module Options (first 200 chars): 1. Slug: "algorithms_and_computational_thinking", Name: "Algorithms & Computational Thinking", Description: "UK strand: Computational Thinking; mirrors GCSE ‘Algorithmic Thinking’...."
2. Slug: "compu...
[2025-06-29 23:19:13,095] INFO - __main__ - main.py:2569 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Calling Gemini API for module inference...
[2025-06-29 23:19:13,889] INFO - __main__ - main.py:2579 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Gemini API call completed in 0.79s. Raw response: 'programming_and_software_development'
[2025-06-29 23:19:13,889] DEBUG - __main__ - main.py:2601 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Cleaned slug: 'programming_and_software_development'
[2025-06-29 23:19:13,890] INFO - __main__ - main.py:2606 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI Inference: Successfully matched module by slug. Chosen: 'programming_and_software_development' (Programming & Software Development)
[2025-06-29 23:19:13,890] INFO - __main__ - main.py:5455 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Successfully inferred module ID via AI: programming_and_software_development
[2025-06-29 23:19:13,891] INFO - __main__ - main.py:5492 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Effective module name for prompt context: 'Programming & Software Development' (Module ID: programming_and_software_development)
[2025-06-29 23:19:14,647] INFO - __main__ - main.py:2136 - No prior student performance document found for Topic: computing_primary-5_computing_programming_and_software_development
[2025-06-29 23:19:17,333] DEBUG - __main__ - main.py:5508 - 🔍 SESSION STATE RETRIEVAL:
[2025-06-29 23:19:17,333] DEBUG - __main__ - main.py:5509 - 🔍   - Session ID: session_0b03d1a2-9188-4ede-960f-5f7c8601eba4
[2025-06-29 23:19:17,334] DEBUG - __main__ - main.py:5510 - 🔍   - Document Exists: True
[2025-06-29 23:19:17,334] DEBUG - __main__ - main.py:5511 - 🔍   - Current Phase: conclusion_summary
[2025-06-29 23:19:17,334] DEBUG - __main__ - main.py:5512 - 🔍   - Probing Level: 5
[2025-06-29 23:19:17,334] DEBUG - __main__ - main.py:5513 - 🔍   - Question Index: 0
[2025-06-29 23:19:17,335] WARNING - __main__ - main.py:5519 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍 SESSION STATE DEBUG:
[2025-06-29 23:19:17,335] WARNING - __main__ - main.py:5520 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍   - Session exists: True
[2025-06-29 23:19:17,335] WARNING - __main__ - main.py:5521 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍   - Current phase: conclusion_summary
[2025-06-29 23:19:17,335] WARNING - __main__ - main.py:5522 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍   - State data keys: ['created_at', 'key_concepts_str_for_ai', 'blooms_levels_str_for_ai', 'quiz_complete', 'last_diagnostic_question_text_asked', 'last_updated', 'student_name', 'quiz_performance', 'current_probing_level_number', 'lesson_context_snapshot', 'is_first_encounter_for_module', 'current_phase', 'current_lesson_phase', 'current_quiz_question', 'teaching_interactions', 'last_modified', 'latest_assessed_level_for_module', 'quiz_answers', 'session_id', 'quiz_questions_generated', 'lesson_start_time', 'teaching_complete', 'assigned_level_for_teaching', 'diagnostic_completed_this_session', 'levels_probed_and_failed', 'last_update_request_id', 'current_session_working_level', 'current_question_index', 'quiz_interactions', 'student_id', 'quiz_started', 'student_answers_for_probing_level']
[2025-06-29 23:19:17,336] DEBUG - __main__ - main.py:5541 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔒🔒🔒 ROBUST STATE PROTECTION INITIATED 🔒🔒🔒
[2025-06-29 23:19:17,336] DEBUG - __main__ - main.py:5542 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] � Retrieved Phase: 'conclusion_summary'
[2025-06-29 23:19:17,336] DEBUG - __main__ - main.py:5543 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] � Diagnostic Completed: False
[2025-06-29 23:19:17,336] DEBUG - __main__ - main.py:5544 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] � Assigned Level: None
[2025-06-29 23:19:17,336] WARNING - __main__ - main.py:5545 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔒 STATE PROTECTION: phase='conclusion_summary', diagnostic_done=False, level=None
[2025-06-29 23:19:17,337] INFO - __main__ - main.py:5589 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ State protection not triggered (diagnostic=False, level=None)
[2025-06-29 23:19:17,337] INFO - __main__ - main.py:5590 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] State protection not triggered
[2025-06-29 23:19:17,337] INFO - __main__ - main.py:5631 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] �🔍 FIRST ENCOUNTER LOGIC:
[2025-06-29 23:19:17,337] INFO - __main__ - main.py:5632 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   assigned_level_for_teaching (session): None
[2025-06-29 23:19:17,337] INFO - __main__ - main.py:5633 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   latest_assessed_level (profile): None
[2025-06-29 23:19:17,337] INFO - __main__ - main.py:5634 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   teaching_level_for_returning_student: None
[2025-06-29 23:19:17,338] INFO - __main__ - main.py:5635 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   has_completed_diagnostic_before: False
[2025-06-29 23:19:17,338] INFO - __main__ - main.py:5636 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   is_first_encounter_for_module: True
[2025-06-29 23:19:17,338] WARNING - __main__ - main.py:5641 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔧 DIAGNOSTIC RESET: First encounter for module - forcing diagnostic_completed_this_session=False
[2025-06-29 23:19:17,338] INFO - __main__ - main.py:5647 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍 PHASE INVESTIGATION:
[2025-06-29 23:19:17,338] INFO - __main__ - main.py:5648 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Retrieved from Firestore: 'conclusion_summary'
[2025-06-29 23:19:17,339] INFO - __main__ - main.py:5649 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Default phase (LESSON_PHASE_DIAGNOSTIC): 'diagnostic_start_probe'
[2025-06-29 23:19:17,339] INFO - __main__ - main.py:5650 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Is first encounter: True
[2025-06-29 23:19:17,339] INFO - __main__ - main.py:5651 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Diagnostic completed: False
[2025-06-29 23:19:17,339] INFO - __main__ - main.py:5657 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ Using stored phase from Firestore: 'conclusion_summary'
[2025-06-29 23:19:17,339] INFO - __main__ - main.py:5671 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] SIMPLIFIED: Trusting AI to determine appropriate starting phase naturally
[2025-06-29 23:19:17,339] INFO - __main__ - main.py:5673 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Final phase for AI logic: conclusion_summary
[2025-06-29 23:19:17,340] INFO - __main__ - main.py:5693 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] NEW SESSION: Forcing question_index to 0 (was: 0)
[2025-06-29 23:19:17,340] INFO - __main__ - main.py:3891 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Diagnostic context validation passed
[2025-06-29 23:19:17,340] INFO - __main__ - main.py:3912 - DETERMINE_PHASE: Preserving advanced phase: 'conclusion_summary' - no backward transitions allowed
[2025-06-29 23:19:17,340] WARNING - __main__ - main.py:5781 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔧 PHASE DETERMINATION OVERRIDE: Using determined phase 'conclusion_summary' for first encounter
[2025-06-29 23:19:17,340] INFO - __main__ - main.py:5802 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Skipping diagnostic context enhancement for non-diagnostic phase: conclusion_summary
[2025-06-29 23:19:17,340] DEBUG - __main__ - main.py:5809 - 🧪 DEBUG PHASE: current_phase_for_ai = 'conclusion_summary'
[2025-06-29 23:19:17,341] DEBUG - __main__ - main.py:5810 - 🧪 DEBUG PHASE: determined_phase = 'conclusion_summary'
[2025-06-29 23:19:17,341] INFO - __main__ - main.py:5816 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Robust context prepared successfully. Phase: conclusion_summary
[2025-06-29 23:19:17,341] DEBUG - __main__ - main.py:5817 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Enhanced context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai']
[2025-06-29 23:19:17,341] WARNING - __main__ - main.py:5987 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🤖 AI PROMPT GENERATION:
[2025-06-29 23:19:17,341] WARNING - __main__ - main.py:5988 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🤖   - Current phase: conclusion_summary
[2025-06-29 23:19:17,342] WARNING - __main__ - main.py:5989 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🤖   - Student query: Yes! Let's wrap it up!...
[2025-06-29 23:19:17,342] WARNING - __main__ - main.py:5990 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🤖   - Context keys: ['student_info', 'lesson_title', 'topic', 'grade', 'level', 'subject', 'country', 'curriculum', 'session_id', 'module_id', 'module_name', 'gs_subject_slug_for_gs', 'gold_standard_module_levels_data', 'local_curriculum_data', 'learning_objectives', 'key_concepts', 'key_concepts_str', 'blooms_levels_str', 'student_performance_history', 'student_performance_db', 'topic_key_for_history', 'is_first_encounter', 'latest_assessed_level', 'lesson_phase', 'current_probing_level_number_from_state', 'current_question_index_from_state', 'student_answers_for_probing_level_from_state', 'levels_probed_and_failed_from_state', 'last_diagnostic_question_text_asked', 'assigned_level_for_teaching', 'current_instructional_step_index_from_context', 'quiz_json_structure_example', 'assessment_json_template_example', 'teaching_interactions', 'quiz_interactions', 'teaching_complete', 'quiz_complete', 'lesson_start_time', 'quiz_questions_generated', 'current_quiz_question', 'quiz_answers', 'quiz_started', 'current_phase_for_ai']
[2025-06-29 23:19:17,342] DEBUG - __main__ - main.py:5993 - 🤖 GENERATING AI PROMPT:
[2025-06-29 23:19:17,342] DEBUG - __main__ - main.py:5994 - 🤖   Phase: conclusion_summary
[2025-06-29 23:19:17,343] DEBUG - __main__ - main.py:5995 - 🤖   Query: Yes! Let's wrap it up!...
[2025-06-29 23:19:17,343] DEBUG - __main__ - main.py:5996 - 🤖   Student: Andrea
[2025-06-29 23:19:17,343] INFO - __main__ - main.py:7009 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] enhance_lesson_content invoked. Query: 'Yes! Let's wrap it up!...'
[2025-06-29 23:19:17,343] INFO - __main__ - main.py:7098 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🎯 CONCLUSION SUMMARY PHASE: Using enhanced handler...
[2025-06-29 23:19:17,343] INFO - __main__ - main.py:14818 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Teaching level search: assigned_level_for_teaching=None, current_session_working_level=None
[2025-06-29 23:19:17,344] INFO - __main__ - main.py:14839 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] No teaching level found in lesson context
[2025-06-29 23:19:17,364] INFO - __main__ - main.py:11085 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Generated 4 homework assignments for 100% performance
[2025-06-29 23:19:17,367] ERROR - __main__ - main.py:11468 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Missing session_id or student_id for completion activities
[2025-06-29 23:19:17,367] INFO - __main__ - main.py:14944 - [REQ 630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Conclusion summary phase completed (Performance: 100%) -> completed
[2025-06-29 23:19:17,368] INFO - __main__ - main.py:7105 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🎯 Enhanced conclusion summary handler completed -> completed
[2025-06-29 23:19:17,368] WARNING - __main__ - main.py:6018 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🤖 AI RESPONSE RECEIVED:
[2025-06-29 23:19:17,369] WARNING - __main__ - main.py:6019 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🤖   - Content length: 1701 chars
[2025-06-29 23:19:17,369] WARNING - __main__ - main.py:6020 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🤖   - State updates: {'new_phase': 'completed'}
[2025-06-29 23:19:17,370] WARNING - __main__ - main.py:6021 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🤖   - Raw state block: None...
[2025-06-29 23:19:17,370] DEBUG - __main__ - main.py:6024 - 🤖 AI RESPONSE PROCESSED:
[2025-06-29 23:19:17,370] DEBUG - __main__ - main.py:6025 - 🤖   Content: 🎓 **Lesson Summary: Advanced Coding Techniques: Variables and Functions**

**Successfully Completed!...
[2025-06-29 23:19:17,371] DEBUG - __main__ - main.py:6026 - 🤖   State: {'new_phase': 'completed'}
[2025-06-29 23:19:17,371] INFO - __main__ - main.py:6052 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] BACKWARD TRANSITION FIX: Phase detection disabled - relying on AI state updates only
[2025-06-29 23:19:17,372] INFO - __main__ - main.py:6053 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] CURRENT PHASE DETERMINATION: AI=completed, Session=conclusion_summary, Final=completed
[2025-06-29 23:19:18,130] INFO - __main__ - main.py:6102 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI processing completed in 0.79s
[2025-06-29 23:19:18,130] WARNING - __main__ - main.py:6113 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍 STATE UPDATE VALIDATION: current_phase='conclusion_summary', new_phase='completed'
[2025-06-29 23:19:18,131] INFO - __main__ - main.py:4096 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI state update validation passed: conclusion_summary → completed
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6122 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ STATE UPDATE VALIDATION PASSED
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6127 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔄 PHASE TRANSITION: conclusion_summary → completed
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6136 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍 COMPLETE PHASE FLOW DEBUG:
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6137 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍   1. Input phase: 'conclusion_summary'
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6138 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍   2. Python calculated next phase: 'NOT_FOUND'
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6139 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍   3. AI state updates: {'new_phase': 'completed'}
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6140 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍   4. Final phase to save: 'completed'
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6143 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 💾 FINAL STATE APPLICATION:
[2025-06-29 23:19:18,131] WARNING - __main__ - main.py:6144 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 💾   - Current phase input: 'conclusion_summary'
[2025-06-29 23:19:18,132] WARNING - __main__ - main.py:6145 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 💾   - State updates from AI: {'new_phase': 'completed'}
[2025-06-29 23:19:18,132] WARNING - __main__ - main.py:6146 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 💾   - Final phase to save: 'completed'
[2025-06-29 23:19:18,132] WARNING - __main__ - main.py:6147 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 💾   - Phase change: True
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4128 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] DIAGNOSTIC_FLOW_METRICS:
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4129 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Phase transition: conclusion_summary -> completed
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4130 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Current level: 5
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4131 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Question index: 0
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4132 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   First encounter: True
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4137 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Answers collected: 5
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4138 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Levels failed: 0
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4096 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI state update validation passed: conclusion_summary → completed
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4142 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   State update valid: True
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:4149 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a]   Diagnostic complete: False
[2025-06-29 23:19:18,132] WARNING - __main__ - main.py:6160 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔧 DIAGNOSTIC INDEX FIX: final_q_index = 0, current_q_index_for_prompt = 0
[2025-06-29 23:19:18,132] INFO - __main__ - main.py:6169 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍 DEBUG state_updates_from_ai teaching_interactions: NOT_FOUND
[2025-06-29 23:19:18,133] INFO - __main__ - main.py:6170 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔍 DEBUG original teaching_interactions: 12
[2025-06-29 23:19:18,962] WARNING - __main__ - main.py:6215 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ SESSION STATE SAVED TO FIRESTORE:
[2025-06-29 23:19:18,962] WARNING - __main__ - main.py:6216 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Phase: completed
[2025-06-29 23:19:18,962] WARNING - __main__ - main.py:6217 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Probing Level: 5
[2025-06-29 23:19:18,962] WARNING - __main__ - main.py:6218 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Question Index: 0
[2025-06-29 23:19:18,963] WARNING - __main__ - main.py:6219 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Diagnostic Complete: False
[2025-06-29 23:19:18,963] WARNING - __main__ - main.py:6226 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Quiz Questions Saved: 10
[2025-06-29 23:19:18,963] WARNING - __main__ - main.py:6227 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Quiz Answers Saved: 10
[2025-06-29 23:19:18,964] WARNING - __main__ - main.py:6228 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Quiz Started: True
[2025-06-29 23:19:18,964] DEBUG - __main__ - main.py:6231 - 🔥 STATE SAVED - Session: session_0b03d1a2-9188-4ede-960f-5f7c8601eba4, Phase: completed
[2025-06-29 23:19:18,964] DEBUG - __main__ - main.py:6232 - 🔥 QUIZ DATA - Questions: 10, Answers: 10
[2025-06-29 23:19:25,332] DEBUG - __main__ - main.py:6290 - ✅ SESSION UPDATED - ID: session_0b03d1a2-9188-4ede-960f-5f7c8601eba4, Phase: completed
[2025-06-29 23:19:25,333] DEBUG - __main__ - main.py:6291 - ✅ INTERACTION LOGGED - Phase: conclusion_summary → completed
[2025-06-29 23:19:25,333] INFO - __main__ - main.py:6297 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ Updated existing session document: session_0b03d1a2-9188-4ede-960f-5f7c8601eba4
[2025-06-29 23:19:25,334] WARNING - __main__ - main.py:6298 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ SESSION UPDATE COMPLETE:
[2025-06-29 23:19:25,334] WARNING - __main__ - main.py:6299 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Session ID: session_0b03d1a2-9188-4ede-960f-5f7c8601eba4
[2025-06-29 23:19:25,335] WARNING - __main__ - main.py:6300 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Phase transition: conclusion_summary → completed
[2025-06-29 23:19:25,335] WARNING - __main__ - main.py:6301 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅   - Interaction logged successfully
[2025-06-29 23:19:25,336] INFO - __main__ - main.py:12928 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI_PERFORMANCE_ASSESSMENT_BLOCK markers not found.
[2025-06-29 23:19:25,336] DEBUG - __main__ - main.py:2898 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] FINAL_ASSESSMENT_BLOCK not found in AI response.
[2025-06-29 23:19:25,336] DEBUG - __main__ - main.py:6356 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] No final assessment data found in AI response
[2025-06-29 23:19:25,337] WARNING - __main__ - main.py:6364 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🎉 LESSON COMPLETION DETECTED!
[2025-06-29 23:19:25,337] WARNING - __main__ - main.py:6365 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🎉   Phase: completed
[2025-06-29 23:19:25,338] WARNING - __main__ - main.py:6366 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🎉   Lesson Complete Flag: False
[2025-06-29 23:19:25,338] WARNING - __main__ - main.py:6367 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🎉   Triggering end-of-lesson reports...
[2025-06-29 23:19:25,339] INFO - __main__ - main.py:6382 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 📊 Calculated lesson score: 100%
[2025-06-29 23:19:25,339] INFO - __main__ - main.py:11085 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Generated 4 homework assignments for 100% performance
[2025-06-29 23:19:25,339] INFO - __main__ - main.py:11471 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Starting lesson completion activities for session session_0b03d1a2-9188-4ede-960f-5f7c8601eba4
[2025-06-29 23:19:25,340] INFO - __main__ - main.py:10441 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Creating comprehensive student subcollections for andrea_ugono_33305
[2025-06-29 23:19:27,014] ERROR - __main__ - main.py:10546 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error creating analytics subcollection: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:27,323] ERROR - __main__ - main.py:10587 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error creating lab_interactions: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:28,318] ERROR - __main__ - main.py:10635 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error creating form_tutor_interactions: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:29,134] ERROR - __main__ - main.py:10685 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error creating lesson_notes: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:29,941] ERROR - __main__ - main.py:10747 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error creating student_reports: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:30,753] ERROR - __main__ - main.py:10811 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error creating progress_summary: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:30,753] INFO - __main__ - main.py:10464 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ Successfully created all student subcollections for andrea_ugono_33305
[2025-06-29 23:19:31,565] ERROR - __main__ - main.py:11554 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error saving homework assignment: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:35,126] WARNING - __main__ - main.py:11613 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] AI insights generation failed: Unterminated string starting at: line 28 column 5 (char 3126)
[2025-06-29 23:19:35,969] ERROR - __main__ - main.py:11744 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error generating comprehensive parent report: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:37,045] ERROR - __main__ - main.py:11862 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Error updating student analytics: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:37,045] INFO - __main__ - main.py:11115 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 📋 Generating consolidated lesson reports for computing_20250629_session_0b03d1a2-9188-4ede-960f-5f7c8601eba4
[2025-06-29 23:19:37,046] INFO - __main__ - main.py:11124 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ Generated comprehensive parent report
[2025-06-29 23:19:37,046] INFO - __main__ - main.py:11133 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ Generated student analytics report
[2025-06-29 23:19:37,046] INFO - __main__ - main.py:11142 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ Generated Bloom's taxonomy report
[2025-06-29 23:19:37,046] INFO - __main__ - main.py:11151 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ Generated lesson summary
[2025-06-29 23:19:37,460] ERROR - __main__ - main.py:11181 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ❌ Error saving consolidated lesson reports: object WriteResult can't be used in 'await' expression
[2025-06-29 23:19:37,461] WARNING - __main__ - main.py:11491 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ⚠️ Consolidated report generation returned no ID
[2025-06-29 23:19:37,461] INFO - __main__ - main.py:11207 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 📈 Updating module progress for computing_default_module
[2025-06-29 23:19:38,210] ERROR - __main__ - main.py:11301 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ❌ Error updating module progress tracking: object DocumentSnapshot can't be used in 'await' expression
[2025-06-29 23:19:38,211] WARNING - __main__ - main.py:11501 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ⚠️ Module progress update returned no data
[2025-06-29 23:19:38,211] INFO - __main__ - main.py:11311 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🔗 Updating student performance for diagnostic compatibility
[2025-06-29 23:19:38,604] ERROR - __main__ - main.py:11367 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ❌ Error updating student performance compatibility: object DocumentSnapshot can't be used in 'await' expression
[2025-06-29 23:19:38,604] INFO - __main__ - main.py:11508 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ Updated student performance for diagnostic compatibility
[2025-06-29 23:19:38,604] INFO - __main__ - main.py:11512 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Completed all lesson completion activities - enhanced functions are defined and ready
[2025-06-29 23:19:38,604] WARNING - __main__ - main.py:6410 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ✅ End-of-lesson reports generation completed successfully!
[2025-06-29 23:19:38,604] DEBUG - __main__ - main.py:6438 - 🔒 COMPREHENSIVE FINAL PHASE CHECK:
[2025-06-29 23:19:38,605] DEBUG - __main__ - main.py:6439 - 🔒   Current Phase: conclusion_summary
[2025-06-29 23:19:38,605] DEBUG - __main__ - main.py:6440 - 🔒   Final Phase: completed
[2025-06-29 23:19:38,605] DEBUG - __main__ - main.py:6441 - 🔒   Diagnostic Complete: False
[2025-06-29 23:19:38,605] DEBUG - __main__ - main.py:6442 - 🔒   Assigned Level: None
[2025-06-29 23:19:38,605] DEBUG - __main__ - main.py:6499 - 🎯 RESPONSE READY:
[2025-06-29 23:19:38,606] DEBUG - __main__ - main.py:6500 - 🎯   Session: session_0b03d1a2-9188-4ede-960f-5f7c8601eba4
[2025-06-29 23:19:38,606] DEBUG - __main__ - main.py:6501 - 🎯   Phase: conclusion_summary → completed
[2025-06-29 23:19:38,606] DEBUG - __main__ - main.py:6502 - 🎯   Content: 🎓 **Lesson Summary: Advanced Coding Techniques: Va...
[2025-06-29 23:19:38,606] DEBUG - __main__ - main.py:6503 - 🎯   Request ID: 630f55f9-48f6-4b0f-94a2-321cc4a1cc2a
[2025-06-29 23:19:38,606] INFO - __main__ - main.py:6509 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] Successfully enhanced content with robust diagnostic flow, returning response via /api/enhance-content
[2025-06-29 23:19:38,607] WARNING - __main__ - main.py:6513 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] 🚨 FINAL SAFETY NET: Detected completion phase 'completed' in final response check
[2025-06-29 23:19:38,919] ERROR - __main__ - main.py:6543 - [630f55f9-48f6-4b0f-94a2-321cc4a1cc2a] ❌ Final safety net error: 'list' object has no attribute 'docs'
[2025-06-29 23:19:38,920] WARNING - __main__ - main.py:766 - High response time detected: 27.43s for enhance_content_api
