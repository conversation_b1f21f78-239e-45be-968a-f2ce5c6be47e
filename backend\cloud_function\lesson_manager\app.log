[2025-06-25 20:36:42,380] INFO - __main__ - main.py:617 - Logging configuration complete with immediate console output
[2025-06-25 20:36:42,384] INFO - __main__ - main.py:693 - INIT_INFO: Flask app instance created and CORS configured.
[2025-06-25 20:36:42,386] INFO - __main__ - main.py:931 - INIT_INFO: Flask app.secret_key SET from FLASK_SECRET_KEY environment variable.
[2025-06-25 20:36:42,398] INFO - __main__ - main.py:960 - Phase transition fixes imported successfully
[2025-06-25 20:36:42,403] INFO - __main__ - main.py:3268 - Successfully imported utils functions
[2025-06-25 20:36:42,406] INFO - __main__ - main.py:3276 - Successfully imported extract_ai_state functions
[2025-06-25 20:36:42,413] INFO - __main__ - main.py:3726 - FLASK: Using unified Firebase initialization approach...
[2025-06-25 20:36:42,417] INFO - unified_firebase_init - unified_firebase_init.py:90 - Attempting Firebase initialization with: firebase-adminsdk-service-key.json
[2025-06-25 20:36:42,474] INFO - unified_firebase_init - unified_firebase_init.py:95 - ✅ Firebase initialized successfully with: firebase-adminsdk-service-key.json
[2025-06-25 20:36:42,475] INFO - unified_firebase_init - unified_firebase_init.py:121 - Testing Firestore connectivity with lightweight operation...
[2025-06-25 20:36:42,985] DEBUG - google.auth.transport.requests - requests.py:185 - Making request: POST https://oauth2.googleapis.com/token
[2025-06-25 20:36:42,987] DEBUG - urllib3.connectionpool - connectionpool.py:1022 - Starting new HTTPS connection (1): oauth2.googleapis.com:443
[2025-06-25 20:36:43,496] DEBUG - urllib3.connectionpool - connectionpool.py:475 - https://oauth2.googleapis.com:443 "POST /token HTTP/1.1" 200 None
[2025-06-25 20:36:44,585] INFO - unified_firebase_init - unified_firebase_init.py:165 - ✅ Firestore connected successfully - connectivity test passed
[2025-06-25 20:36:44,586] INFO - __main__ - main.py:3734 - FLASK: Unified Firebase initialization successful - Firestore client ready
[2025-06-25 20:36:44,586] INFO - __main__ - main.py:3824 - Gemini API will be initialized on first use (lazy loading).
[2025-06-25 20:36:44,688] INFO - __main__ - main.py:1101 - Successfully imported timetable_generator functions
[2025-06-25 20:36:44,726] DEBUG - google.auth._default - _default.py:256 - Checking solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json for explicit credentials as part of auth process...
[2025-06-25 20:36:44,871] DEBUG - google.auth._default - _default.py:256 - Checking solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json for explicit credentials as part of auth process...
[2025-06-25 20:36:44,945] INFO - __main__ - main.py:17026 - Google Cloud Storage client initialized successfully.
[2025-06-25 20:36:45,012] INFO - __main__ - main.py:17918 - Starting Lesson Manager Service...
[2025-06-25 20:36:45,022] INFO - __main__ - main.py:17933 - Flask server starting on host 0.0.0.0, port 5000
[2025-06-25 20:36:45,022] INFO - __main__ - main.py:17934 - Debug mode: ON
[2025-06-25 20:36:45,141] INFO - werkzeug - _internal.py:97 - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
[2025-06-25 20:36:45,141] INFO - werkzeug - _internal.py:97 - [33mPress CTRL+C to quit[0m
