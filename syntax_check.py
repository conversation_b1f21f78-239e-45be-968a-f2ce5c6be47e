#!/usr/bin/env python3
"""
Quick syntax checker for main.py
"""
import sys
import ast

def check_syntax(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to parse the file
        ast.parse(content)
        print("✅ Syntax is valid!")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax Error:")
        print(f"   Line {e.lineno}: {e.text}")
        print(f"   Error: {e.msg}")
        return False
    
    except UnicodeDecodeError as e:
        print(f"❌ Unicode Error: {e}")
        return False
    
    except Exception as e:
        print(f"❌ Other Error: {e}")
        return False

if __name__ == "__main__":
    filepath = r"C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\main.py"
    check_syntax(filepath)
