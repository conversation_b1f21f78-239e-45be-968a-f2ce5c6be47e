#!/usr/bin/env python3
"""Quick health check test"""

import requests
import time

def test_health():
    """Test if backend is running"""
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        print(f"Health check: {response.status_code} - {response.text}")
        return True
    except Exception as e:
        print(f"Backend not running: {e}")
        return False

def start_backend():
    """Try to start the backend"""
    import subprocess
    try:
        print("Starting backend...")
        process = subprocess.Popen(["python", "main.py"], 
                                 cwd=r"c:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager")
        time.sleep(3)  # Give it time to start
        return process
    except Exception as e:
        print(f"Failed to start backend: {e}")
        return None

if __name__ == "__main__":
    print("Testing backend health...")
    
    if not test_health():
        print("Backend not running, attempting to start...")
        process = start_backend()
        time.sleep(5)  # Wait for startup
        
        if test_health():
            print("Backend started successfully!")
        else:
            print("Failed to start backend")
    else:
        print("Backend is already running!")
