#!/usr/bin/env python3
"""
Test if backend is accessible and write results to file
"""
import requests
import json
from datetime import datetime

def test_and_write_results():
    results = {
        "test_time": str(datetime.now()),
        "tests": []
    }
    
    # Test 1: Health check
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        results["tests"].append({
            "test": "health_check",
            "status": "success" if response.status_code == 200 else "failed",
            "status_code": response.status_code,
            "response": response.text[:200] if hasattr(response, 'text') else "No response text"
        })
    except Exception as e:
        results["tests"].append({
            "test": "health_check",
            "status": "error",
            "error": str(e)
        })
    
    # Test 2: API endpoint
    try:
        test_data = {
            "session_id": "connectivity_test",
            "student_id": "test_student",
            "lesson_ref": "P5-COM-001",
            "content_to_enhance": "Start lesson",
            "grade": "Primary 5",
            "subject": "Computing",
            "level": "5",
            "student_info": {"first_name": "TestStudent"},
            "chat_history": []
        }
        
        response = requests.post(
            "http://localhost:5000/api/enhance-content",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            phase = result.get('data', {}).get('current_phase', 'Unknown')
            results["tests"].append({
                "test": "api_endpoint",
                "status": "success",
                "current_phase": phase,
                "response_preview": str(result)[:300]
            })
        else:
            results["tests"].append({
                "test": "api_endpoint",
                "status": "failed",
                "status_code": response.status_code,
                "response": response.text[:200] if hasattr(response, 'text') else "No response text"
            })
            
    except Exception as e:
        results["tests"].append({
            "test": "api_endpoint",
            "status": "error",
            "error": str(e)
        })
    
    # Write results to file
    with open("backend_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print("Test completed - results written to backend_test_results.json")

if __name__ == "__main__":
    test_and_write_results()
