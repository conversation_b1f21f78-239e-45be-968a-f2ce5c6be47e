#!/usr/bin/env python3

import requests
import json

def test_debug_endpoint():
    print("="*60)
    print("TESTING DEBUG ENDPOINT")
    print("="*60)
    try:
        response = requests.post('http://localhost:5000/debug/enhance-content-full-test',
                               json={'session_id': 'test', 'message': 'Start lesson'},
                               timeout=10)
        print(f'Full debug endpoint status: {response.status_code}')

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            print(f'Final phase: {data.get("final_phase", "NOT_FOUND")}')
            print(f'Enforcement applied: {data.get("enforcement_applied", "NOT_FOUND")}')
            print(f'Final enforcement applied: {data.get("final_enforcement_applied", "NOT_FOUND")}')
            print(f'Python calculated phase: {data.get("python_calculated_phase", "NOT_FOUND")}')
            print(f'Current phase: {data.get("current_phase", "NOT_FOUND")}')
            print(f'AI proposed phase: {data.get("ai_proposed_phase", "NOT_FOUND")}')

            # Check if enforcement is working
            if data.get("final_phase") == "diagnostic_probing_L5_ask_q1":
                print("✅ DEBUG ENDPOINT: ENFORCEMENT WORKING! Phase correctly transitioned.")
            else:
                print("❌ DEBUG ENDPOINT: ENFORCEMENT NOT WORKING! Phase did not transition.")
        else:
            print(f'Error response: {response.text}')

    except Exception as e:
        print(f'Full debug endpoint failed: {e}')

def test_main_api_endpoint():
    print("\n" + "="*60)
    print("TESTING MAIN API ENDPOINT")
    print("="*60)
    try:
        test_session = f"diag_test_{int(__import__('time').time())}"
        request_data = {
            "session_id": test_session,
            "student_id": "diag_test_student",
            "lesson_ref": "P5-COM-001",
            "content_to_enhance": "Start lesson",
            "grade": "Primary 5",
            "subject": "Computing",
            "level": "5",
            "student_info": {"first_name": "DiagTest"},
            "chat_history": []
        }

        response = requests.post('http://localhost:5000/api/enhance-content',
                               json=request_data, timeout=30)
        print(f'Main API endpoint status: {response.status_code}')

        if response.status_code == 200:
            result = response.json()
            data = result.get('data', {})
            debug_info = data.get('debug_info', {})
            enforcement_tracking = debug_info.get('enforcement_tracking', {})

            print(f'Current phase: {data.get("current_phase", "NOT_FOUND")}')
            print(f'Python calculated phase: {enforcement_tracking.get("python_calculated_phase", "NOT_FOUND")}')
            print(f'AI proposed phase: {enforcement_tracking.get("ai_proposed_phase", "NOT_FOUND")}')
            print(f'Enforcement applied: {enforcement_tracking.get("enforcement_applied", "NOT_FOUND")}')
            print(f'Final enforcement applied: {enforcement_tracking.get("final_enforcement_applied", "NOT_FOUND")}')
            print(f'Enforcement reason: {enforcement_tracking.get("enforcement_reason", "NOT_FOUND")}')

            # Check if enforcement is working
            if data.get("current_phase") == "diagnostic_probing_L5_ask_q1":
                print("✅ MAIN API: ENFORCEMENT WORKING! Phase correctly transitioned.")
            else:
                print("❌ MAIN API: ENFORCEMENT NOT WORKING! Phase did not transition.")
                print(f"Expected: diagnostic_probing_L5_ask_q1, Got: {data.get('current_phase')}")
        else:
            print(f'Error response: {response.text}')

    except Exception as e:
        print(f'Main API endpoint failed: {e}')

if __name__ == "__main__":
    test_debug_endpoint()
    test_main_api_endpoint()
