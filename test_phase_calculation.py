#!/usr/bin/env python3
"""
Simple Phase Calculation Test
Tests the calculate_next_mandatory_phase function directly.
"""

import sys
import os
import json
import time
from datetime import datetime

# Add the backend path to sys.path
backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
sys.path.insert(0, backend_path)

try:
    from main import calculate_next_mandatory_phase
    print("✓ Successfully imported calculate_next_mandatory_phase")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

class PhaseCalculationTest:
    def __init__(self):
        self.test_results = []
        
    def log_test(self, test_name, passed, details=""):
        """Log test result"""
        result = {
            "test": test_name,
            "passed": passed,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status = "✓" if passed else "✗"
        print(f"{status} {test_name}: {details}")
        
    def test_phase_calculation(self, current_phase, expected_next_phase):
        """Test phase calculation logic"""
        try:
            # Use a test request ID
            request_id = "test_request_001"
            calculated_phase = calculate_next_mandatory_phase(current_phase, request_id)
            passed = calculated_phase == expected_next_phase
            self.log_test(
                f"Phase calculation: {current_phase} -> {expected_next_phase}",
                passed,
                f"Got: {calculated_phase}, Expected: {expected_next_phase}"
            )
            return passed
        except Exception as e:
            self.log_test(
                f"Phase calculation: {current_phase} -> {expected_next_phase}",
                False,
                f"Exception: {str(e)}"
            )
            return False
    
    def test_all_phase_transitions(self):
        """Test all expected phase transitions"""
        print("\n" + "="*60)
        print("TESTING ALL PHASE TRANSITIONS")
        print("="*60)
        
        # Test diagnostic phase progression
        print("\n--- Diagnostic Phase Progression ---")
        self.test_phase_calculation("diagnostic_start_probe", "diagnostic_probing_L5_ask_q1")
        self.test_phase_calculation("diagnostic_probing_L5_ask_q1", "diagnostic_probing_L5_eval_q1_ask_q2")
        self.test_phase_calculation("diagnostic_probing_L5_eval_q1_ask_q2", "diagnostic_probing_L5_eval_q2_ask_q3")
        self.test_phase_calculation("diagnostic_probing_L5_eval_q2_ask_q3", "diagnostic_probing_L5_eval_q3_ask_q4")
        self.test_phase_calculation("diagnostic_probing_L5_eval_q3_ask_q4", "diagnostic_probing_L5_eval_q4_ask_q5")
        self.test_phase_calculation("diagnostic_probing_L5_eval_q4_ask_q5", "diagnostic_probing_L5_eval_q5_decide_level")
        self.test_phase_calculation("diagnostic_probing_L5_eval_q5_decide_level", "teaching_start_level_5")
        
        # Test teaching phase progression
        print("\n--- Teaching Phase Progression ---")
        self.test_phase_calculation("teaching_start_level_5", "teaching")
        self.test_phase_calculation("teaching", "quiz_initiate")
        
        # Test quiz phase progression
        print("\n--- Quiz Phase Progression ---")
        self.test_phase_calculation("quiz_initiate", "quiz_questions")
        self.test_phase_calculation("quiz_questions", "quiz_results")
        self.test_phase_calculation("quiz_results", "conclusion_summary")
        
        # Test conclusion and completion
        print("\n--- Conclusion and Completion ---")
        self.test_phase_calculation("conclusion_summary", "final_assessment_pending")
        self.test_phase_calculation("final_assessment_pending", "completed")
        self.test_phase_calculation("completed", "completed")  # Should stay completed
        
    def test_different_levels(self):
        """Test diagnostic progression for different levels"""
        print("\n--- Different Level Progression ---")
        
        # Test Level 3
        self.test_phase_calculation("diagnostic_start_probe_L3", "diagnostic_probing_L3_ask_q1")
        self.test_phase_calculation("diagnostic_probing_L3_eval_q5_decide_level", "teaching_start_level_3")
        self.test_phase_calculation("teaching_start_level_3", "teaching")
        
        # Test Level 6
        self.test_phase_calculation("diagnostic_start_probe_L6", "diagnostic_probing_L6_ask_q1")
        self.test_phase_calculation("diagnostic_probing_L6_eval_q5_decide_level", "teaching_start_level_6")
        self.test_phase_calculation("teaching_start_level_6", "teaching")
    
    def test_edge_cases(self):
        """Test edge cases and unknown phases"""
        print("\n--- Edge Cases ---")
        
        # Test unknown phase - should return the same phase
        self.test_phase_calculation("unknown_phase", "unknown_phase")
        
        # Test malformed phase names
        self.test_phase_calculation("", "")
        self.test_phase_calculation("malformed_phase_name", "malformed_phase_name")
    
    def generate_report(self):
        """Generate final test report"""
        print("\n" + "="*60)
        print("FINAL TEST REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["passed"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\nFailed Tests:")
            for result in self.test_results:
                if not result["passed"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        # Save detailed report
        report_filename = f"phase_calculation_test_report_{int(time.time())}.json"
        with open(report_filename, 'w') as f:
            json.dump({
                "summary": {
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "success_rate": (passed_tests/total_tests)*100,
                    "timestamp": datetime.now().isoformat()
                },
                "detailed_results": self.test_results
            }, f, indent=2)
        
        print(f"\nDetailed report saved to: {report_filename}")
        
        return passed_tests == total_tests

def main():
    """Main test execution"""
    print("🧪 Phase Calculation Test")
    print("Testing the calculate_next_mandatory_phase function")
    print("="*50)
    
    test_suite = PhaseCalculationTest()
    
    # Test all phase transitions
    test_suite.test_all_phase_transitions()
    
    # Test different levels
    test_suite.test_different_levels()
    
    # Test edge cases
    test_suite.test_edge_cases()
    
    # Generate final report
    all_passed = test_suite.generate_report()
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED! The phase calculation logic is working correctly.")
        print("✅ Phase progression is robust and handles all expected scenarios.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED. Check the report for details.")
        print("🔧 Phase calculation logic needs fixes.")
        sys.exit(1)

if __name__ == "__main__":
    main()
