#!/usr/bin/env python3
"""
END-TO-E<PERSON> FRONTEND PATH TEST
This test verifies that the frontend API path now works correctly after unification fixes.
It simulates the exact "Start Lesson" button flow through all 15 phases.
"""

import json
import time
import uuid
import requests
from datetime import datetime
import sys

# Configuration
BASE_URL = "http://localhost:5000"
ENHANCE_CONTENT_URL = f"{BASE_URL}/api/enhance-content"

# Test data that matches frontend requests
TEST_SESSION_ID = f"frontend_e2e_{int(time.time())}"
TEST_STUDENT_ID = "test_student_frontend"
TEST_LESSON_REF = "P5-COM-001"

# Expected phase progression (15 phases total)
EXPECTED_PHASES = [
    "diagnostic_start_probe",
    "diagnostic_probing_L5_ask_q1", 
    "diagnostic_probing_L5_eval_q1_ask_q2",
    "diagnostic_probing_L5_eval_q2_ask_q3",
    "diagnostic_probing_L5_eval_q3_ask_q4", 
    "diagnostic_probing_L5_eval_q4_ask_q5",
    "diagnostic_probing_L5_eval_q5_decide_level",
    "teaching_start_level_5",
    "teaching",
    "quiz_initiate",
    "quiz_questions", 
    "quiz_results",
    "conclusion_summary",
    "final_assessment_pending",
    "completed"
]

def check_backend_health():
    """Check if the backend server is running."""
    try:
        # Try multiple health endpoints for compatibility
        health_endpoints = ["/health", "/api/health", "/health-check"]
        
        for endpoint in health_endpoints:
            try:
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
                if response.status_code == 200:
                    print(f"✅ Backend server is running (via {endpoint})")
                    return True
                else:
                    print(f"⚠️ Backend health check via {endpoint} returned {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"⚠️ Health check via {endpoint} failed: {e}")
                continue
        
        print("❌ All health check endpoints failed")
        print("💡 Start the backend with: python backend/cloud_function/lesson_manager/main.py")
        return False
        
    except Exception as e:
        print(f"❌ Backend health check error: {e}")
        print("💡 Start the backend with: python backend/cloud_function/lesson_manager/main.py")
        return False

def make_frontend_request(content, chat_history=None, step_name=""):
    """Make a request to the frontend API endpoint."""
    
    request_data = {
        "session_id": TEST_SESSION_ID,
        "student_id": TEST_STUDENT_ID,
        "lesson_ref": TEST_LESSON_REF,
        "content_to_enhance": content,
        "grade": "Primary 5",
        "subject": "Computing", 
        "level": "5",
        "student_info": {
            "first_name": "TestStudent",
            "country": "Nigeria"
        },
        "chat_history": chat_history or []
    }
    
    print(f"\n🚀 {step_name}")
    print(f"📤 Request: {content}")
    
    try:
        response = requests.post(ENHANCE_CONTENT_URL, json=request_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            
            # Extract key information
            current_phase = result.get('data', {}).get('current_phase', 'Unknown')
            enhanced_content = result.get('data', {}).get('enhanced_content', '')
            
            print(f"📥 Response phase: {current_phase}")
            print(f"📝 Content preview: {enhanced_content[:100]}...")
            
            return {
                'success': True,
                'current_phase': current_phase,
                'response': enhanced_content,
                'full_result': result
            }
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Error: {response.text}")
            return {
                'success': False,
                'error': f"HTTP {response.status_code}: {response.text}"
            }
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def run_complete_lesson_flow():
    """Run the complete end-to-end lesson flow through all 15 phases."""
    
    print(f"\n" + "=" * 60)
    print("🎯 COMPLETE FRONTEND LESSON FLOW TEST")
    print("=" * 60)
    
    # Simulate student responses for interactive phases
    student_responses = [
        "I'm ready to start the lesson!",  # Initial start
        "I think the answer is A",  # Diagnostic questions
        "The answer should be B",
        "I believe it's C", 
        "My answer is D",
        "I choose option A",
        "Thank you for the explanation",  # Teaching phase
        "I understand now",
        "Yes, let's take the quiz",  # Quiz start
        "The answer is A",  # Quiz questions
        "I think it's B",
        "The correct answer is C",
        "Thank you for the summary",  # Conclusion
        "I'm ready for final assessment",
        "Great! I completed the lesson"
    ]
    
    chat_history = []
    current_phase = None
    
    for i, response in enumerate(student_responses):
        if i >= len(EXPECTED_PHASES):
            break
            
        step_name = f"Step {i+1}: {EXPECTED_PHASES[i] if i < len(EXPECTED_PHASES) else 'Additional'}"
        
        result = make_frontend_request(response, chat_history, step_name)
        
        if not result['success']:
            print(f"❌ Failed at step {i+1}: {result['error']}")
            return False
        
        current_phase = result['current_phase']
        
        # Update chat history
        chat_history.append({
            "role": "user",
            "content": response
        })
        chat_history.append({
            "role": "assistant", 
            "content": result['response']
        })
        
        # Check if we've reached completion
        if current_phase == 'completed':
            print(f"\n✅ SUCCESS: Reached completion at step {i+1}")
            print("🎉 All 15 phases completed successfully!")
            return True
        
        # Prevent infinite loops
        if i > 20:  # Safety limit
            print(f"\n⚠️ Stopping after {i+1} steps to prevent infinite loop")
            break
            
        # Brief pause between requests
        time.sleep(0.5)
    
    # Check final status
    if current_phase == 'completed':
        print(f"\n✅ LESSON COMPLETE!")
        print("🎉 Frontend path works correctly - all phases reached!")
        return True
    else:
        print(f"\n❌ INCOMPLETE: Only reached {current_phase}")
        print("❌ Still needs fixes")
        return False

def test_diagnostic_progression_specifically():
    """Test specifically the diagnostic phase progression that was problematic."""
    
    print(f"\n" + "=" * 60)
    print("🔍 DIAGNOSTIC PHASE SPECIFIC TEST")
    print("=" * 60)
    
    # Test just the first few diagnostic transitions
    test_session = f"diag_test_{int(time.time())}"
    
    # 1. Start with diagnostic_start_probe
    request_data = {
        "session_id": test_session,
        "student_id": "diag_test_student",
        "lesson_ref": TEST_LESSON_REF,
        "content_to_enhance": "Start lesson",
        "grade": "Primary 5",
        "subject": "Computing", 
        "level": "5",
        "student_info": {"first_name": "DiagTest"},
        "chat_history": []
    }
    
    print("🧪 Testing diagnostic_start_probe → diagnostic_probing_L5_ask_q1")
    
    try:
        response = requests.post(ENHANCE_CONTENT_URL, json=request_data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            phase = result.get('data', {}).get('current_phase', 'Unknown')
            
            if phase == 'diagnostic_probing_L5_ask_q1':
                print("✅ DIAGNOSTIC PROGRESSION FIXED!")
                print(f"   diagnostic_start_probe → {phase}")
                return True
            else:
                print(f"❌ Still stuck in: {phase}")
                return False
        else:
            print(f"❌ Request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    print("Starting end-to-end frontend path test...\n")
    
    # Check if backend is running
    if not check_backend_health():
        sys.exit(1)
    
    # Run diagnostic-specific test first
    diag_success = test_diagnostic_progression_specifically()
    
    if diag_success:
        print("\n🚀 Diagnostic progression works! Running full end-to-end test...")
        # Run complete lesson flow
        success = run_complete_lesson_flow()
        
        print(f"\n🏁 END-TO-END FRONTEND TEST COMPLETED")
        print(f"Status: {'✅ SUCCESS' if success else '❌ NEEDS MORE WORK'}")
        
        if success:
            print("\n🎉 FRONTEND/BACKEND UNIFICATION VERIFIED!")
            print("The frontend 'Start Lesson' path now works correctly.")
        else:
            print("\n🔧 PARTIAL SUCCESS - Frontend progresses but may need more interactions.")
    else:
        print("\n❌ DIAGNOSTIC PROGRESSION STILL BROKEN")
        print("🔧 The unification fixes may need additional debugging.")
        
    print(f"\nTest completed at {datetime.now()}")
