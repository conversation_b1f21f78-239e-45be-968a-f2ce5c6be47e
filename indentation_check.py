#!/usr/bin/env python3
"""
Check for indentation issues in main.py
"""
import re

def check_indentation_issues(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        issues = []
        
        for i, line in enumerate(lines, 1):
            # Check for mixed tabs and spaces
            if '\t' in line and ' ' * 4 in line:
                issues.append(f"Line {i}: Mixed tabs and spaces")
            
            # Check for inconsistent indentation
            if line.strip() and not line.startswith('#'):
                leading_whitespace = len(line) - len(line.lstrip())
                if leading_whitespace % 4 != 0 and leading_whitespace > 0:
                    issues.append(f"Line {i}: Inconsistent indentation ({leading_whitespace} spaces)")
        
        if issues:
            print("❌ Indentation issues found:")
            for issue in issues[:10]:  # Show first 10 issues
                print(f"   {issue}")
            if len(issues) > 10:
                print(f"   ... and {len(issues) - 10} more issues")
        else:
            print("✅ No indentation issues found")
            
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ Error checking indentation: {e}")
        return False

if __name__ == "__main__":
    filepath = r"C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\main.py"
    check_indentation_issues(filepath)
