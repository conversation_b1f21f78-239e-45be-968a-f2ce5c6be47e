#!/usr/bin/env python3
"""
Simple backend connectivity test
"""
import requests
import time

def test_backend_connection():
    print("🔍 Testing backend connection...")
    
    try:
        # Test health endpoint
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check passed!")
            print(f"Response: {response.text}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend - is it running on port 5000?")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_api_endpoint():
    print("\n🔍 Testing API endpoint...")
    
    try:
        test_data = {
            "session_id": "test_session",
            "student_id": "test_student",
            "lesson_ref": "P5-COM-001",
            "content_to_enhance": "Start lesson",
            "grade": "Primary 5",
            "subject": "Computing",
            "level": "5",
            "student_info": {"first_name": "TestStudent"},
            "chat_history": []
        }
        
        response = requests.post(
            "http://localhost:5000/api/enhance-content",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            phase = result.get('data', {}).get('current_phase', 'Unknown')
            print(f"✅ API endpoint works! Current phase: {phase}")
            return True
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API test error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Backend Connectivity Test")
    print("=" * 40)
    
    # Give the server time to fully start up
    time.sleep(2)
    
    if test_backend_connection():
        test_api_endpoint()
    else:
        print("\n💡 Make sure the backend is running with:")
        print("   python backend/cloud_function/lesson_manager/main.py")
