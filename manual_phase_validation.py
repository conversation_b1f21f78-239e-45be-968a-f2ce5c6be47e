#!/usr/bin/env python3
"""
Manual Phase Validation Test
Manually validate the phase calculation logic by testing the function in isolation.
"""

# Extracted phase calculation logic for direct testing
def calculate_next_mandatory_phase(current_phase: str, request_id: str) -> str:
    """
    Manual implementation of the phase calculation logic for testing.
    This replicates the logic from main.py for validation.
    """
    import re
    
    try:
        # DIAGNOSTIC PHASES (8-step sequence)
        if current_phase.startswith('diagnostic_'):
            # Extract the level number (e.g., '5' from '..._L5_...')
            level_match = re.search(r'_L(\d+)_', current_phase)
            level_num = level_match.group(1) if level_match else "5" # Default to 5 if not found

            # --- Mandatory 8-Phase Diagnostic Progression Rules ---
            if current_phase == 'diagnostic_start_probe':
                return f'diagnostic_probing_L{level_num}_ask_q1'
            
            # Handle intermediate evaluation/ask pairs (q1->q2, q2->q3, etc.)
            eval_match = re.search(r'_eval_q(\d+)_ask_q(\d+)', current_phase)
            if eval_match:
                ask_q_num = int(eval_match.group(2))
                if ask_q_num < 5:
                    return f'diagnostic_probing_L{level_num}_eval_q{ask_q_num}_ask_q{ask_q_num + 1}'
                else:
                    return f'diagnostic_probing_L{level_num}_eval_q5_decide_level'

            # Handle transition from first question to first evaluation
            if current_phase.endswith('_ask_q1'):
                return f'diagnostic_probing_L{level_num}_eval_q1_ask_q2'

            # Handle final decision step - transition to teaching
            if current_phase.endswith('_eval_q5_decide_level'):
                return f'teaching_start_level_{level_num}'

        # TEACHING PHASES
        elif current_phase.startswith('teaching_start_level_'):
            # Always transition from teaching_start_level_X to teaching
            return 'teaching'
            
        elif current_phase == 'teaching':
            # After sufficient teaching interactions, transition to quiz
            return 'quiz_initiate'

        # QUIZ PHASES
        elif current_phase == 'quiz_initiate':
            # Always transition to quiz_questions after initiation
            return 'quiz_questions'
            
        elif current_phase == 'quiz_questions':
            # After all quiz questions are completed, show results
            return 'quiz_results'
            
        elif current_phase == 'quiz_results':
            # After showing results, move to conclusion
            return 'conclusion_summary'

        # CONCLUSION AND COMPLETION PHASES
        elif current_phase == 'conclusion_summary':
            # After summary, move to final assessment
            return 'final_assessment_pending'
            
        elif current_phase == 'final_assessment_pending':
            # Complete the lesson
            return 'completed'
            
        elif current_phase == 'completed':
            # Lesson is complete - stay completed
            return 'completed'

        # FALLBACK FOR UNKNOWN PHASES
        else:
            print(f"WARNING: Unhandled phase '{current_phase}'. Returning current phase.")
            return current_phase

    except Exception as e:
        print(f"ERROR in calculate_next_mandatory_phase: {e}")
        return current_phase # Fallback to the current phase on error

def run_validation_tests():
    """Run comprehensive validation tests"""
    print("🧪 LESSON PHASE PROGRESSION VALIDATION TEST")
    print("="*60)
    
    test_cases = [
        # Diagnostic progression
        ("diagnostic_start_probe", "diagnostic_probing_L5_ask_q1"),
        ("diagnostic_probing_L5_ask_q1", "diagnostic_probing_L5_eval_q1_ask_q2"),
        ("diagnostic_probing_L5_eval_q1_ask_q2", "diagnostic_probing_L5_eval_q2_ask_q3"),
        ("diagnostic_probing_L5_eval_q2_ask_q3", "diagnostic_probing_L5_eval_q3_ask_q4"),
        ("diagnostic_probing_L5_eval_q3_ask_q4", "diagnostic_probing_L5_eval_q4_ask_q5"),
        ("diagnostic_probing_L5_eval_q4_ask_q5", "diagnostic_probing_L5_eval_q5_decide_level"),
        ("diagnostic_probing_L5_eval_q5_decide_level", "teaching_start_level_5"),
        
        # Teaching progression
        ("teaching_start_level_5", "teaching"),
        ("teaching_start_level_3", "teaching"),
        ("teaching_start_level_6", "teaching"),
        ("teaching", "quiz_initiate"),
        
        # Quiz progression
        ("quiz_initiate", "quiz_questions"),
        ("quiz_questions", "quiz_results"),
        ("quiz_results", "conclusion_summary"),
        
        # Completion progression
        ("conclusion_summary", "final_assessment_pending"),
        ("final_assessment_pending", "completed"),
        ("completed", "completed"),  # Should stay completed
        
        # Edge cases
        ("unknown_phase", "unknown_phase"),  # Should return same phase
        ("", ""),  # Empty string
    ]
    
    passed = 0
    failed = 0
    
    for current_phase, expected_next in test_cases:
        try:
            result = calculate_next_mandatory_phase(current_phase, "test_request")
            if result == expected_next:
                print(f"✓ {current_phase} -> {result}")
                passed += 1
            else:
                print(f"✗ {current_phase} -> {result} (expected: {expected_next})")
                failed += 1
        except Exception as e:
            print(f"✗ {current_phase} -> ERROR: {e}")
            failed += 1
    
    print("\n" + "="*60)
    print("VALIDATION RESULTS")
    print("="*60)
    print(f"Total Tests: {passed + failed}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed/(passed + failed))*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The lesson phase progression logic is working correctly.")
        print("✅ All phase transitions are properly handled.")
        print("✅ The system is ready for end-to-end lesson flow.")
    else:
        print(f"\n❌ {failed} TESTS FAILED")
        print("🔧 The lesson phase progression logic needs fixes.")

if __name__ == "__main__":
    run_validation_tests()
