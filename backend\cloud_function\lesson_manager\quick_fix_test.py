#!/usr/bin/env python3
"""Quick test to verify the diagnostic progression fix"""

import requests
import json
import time

def test_diagnostic_progression():
    """Test if the diagnostic progression fix works"""
    base_url = "http://localhost:5000"
    
    # Test data
    test_data = {
        "user_query": "Hello, I'm ready to start!",
        "session_id": f"test_fix_{int(time.time())}",
        "student_id": "test_student",
        "lesson_ref": "P5-COM-001",
        "current_phase": "diagnostic_start_probe"
    }
    
    try:
        print("Testing diagnostic progression fix...")
        print(f"Sending request to {base_url}/api/enhance-content")
        
        response = requests.post(
            f"{base_url}/api/enhance-content",
            headers={"Content-Type": "application/json"},
            json=test_data,
            timeout=10
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response received!")
            
            # Check if we got the expected phase progression
            if 'data' in data and 'state_updates' in data['data']:
                state_updates = data['data']['state_updates']
                new_phase = state_updates.get('new_phase')
                print(f"Phase progression: diagnostic_start_probe → {new_phase}")
                
                if new_phase == "diagnostic_probing_L5_ask_q1":
                    print("✅ DIAGNOSTIC PROGRESSION FIXED!")
                    print("The enforcement logic is working correctly.")
                    return True
                else:
                    print(f"❌ Still stuck or wrong progression: {new_phase}")
                    return False
            else:
                print("❌ No state updates in response")
                return False
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server")
        print("Please start the backend with: python main.py")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

if __name__ == "__main__":
    success = test_diagnostic_progression()
    if success:
        print("\n🎉 Diagnostic progression fix is working!")
    else:
        print("\n💥 Fix needs more debugging.")
