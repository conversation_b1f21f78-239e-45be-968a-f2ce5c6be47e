import requests
import time
import json

def test_backend_connectivity():
    base_url = "http://localhost:5000"
    results = {"timestamp": time.time(), "tests": []}
    
    # Test health endpoint
    print("Testing backend connectivity...")
    results["tests"].append({"test": "health_check", "status": "starting"})
    
    try:
        response = requests.get(f"{base_url}/api/health", timeout=5)
        print(f"Health endpoint: {response.status_code}")
        results["tests"][-1]["status"] = f"status_{response.status_code}"
        
        if response.status_code == 200:
            print("✅ Backend is accessible")
            results["backend_accessible"] = True
            
            # Test the main API endpoint with minimal data
            test_data = {
                "session_id": "test_session",
                "student_id": "test_student", 
                "lesson_ref": "P5-COM-001",
                "content_to_enhance": "Start lesson",
                "grade": "Primary 5",
                "subject": "Computing",
                "level": "5",
                "student_info": {"first_name": "TestStudent"},
                "chat_history": []
            }
            
            print("Testing main API endpoint...")
            results["tests"].append({"test": "api_endpoint", "status": "starting"})
            
            api_response = requests.post(f"{base_url}/api/enhance-content", json=test_data, timeout=30)
            print(f"API endpoint: {api_response.status_code}")
            results["tests"][-1]["status"] = f"status_{api_response.status_code}"
            
            if api_response.status_code == 200:
                result = api_response.json()
                phase = result.get('data', {}).get('current_phase', 'Unknown')
                print(f"✅ API working! Current phase: {phase}")
                results["api_working"] = True
                results["current_phase"] = phase
                
                # Check if we got past diagnostic_start_probe
                if phase != 'diagnostic_start_probe':
                    print(f"🎉 UNIFICATION WORKING! Progressed to: {phase}")
                    results["unification_working"] = True
                else:
                    print(f"⚠️ Still in diagnostic_start_probe - may need debugging")
                    results["unification_working"] = False
                    
            else:
                print(f"❌ API error: {api_response.text}")
                results["api_working"] = False
                results["api_error"] = api_response.text
                
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            results["backend_accessible"] = False
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        results["connection_error"] = str(e)
    
    # Write results to file
    with open("connectivity_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"Results saved to connectivity_test_results.json")

if __name__ == "__main__":
    test_backend_connectivity()
