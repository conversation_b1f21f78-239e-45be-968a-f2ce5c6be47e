#!/usr/bin/env python3
"""
Simple health check test to verify the unified health endpoints work.
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_health_endpoints():
    """Test all health endpoints to verify unification worked."""
    
    endpoints = ["/health", "/api/health", "/health-check"]
    
    print("🏥 TESTING UNIFIED HEALTH ENDPOINTS")
    print("=" * 50)
    
    for endpoint in endpoints:
        print(f"\n🔍 Testing {endpoint}...")
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {endpoint} - STATUS: {response.status_code}")
                
                # Try to parse JSON response
                try:
                    data = response.json()
                    status = data.get('status', 'unknown')
                    print(f"   📊 Health Status: {status}")
                    
                    if 'timestamp' in data:
                        print(f"   🕒 Timestamp: {data['timestamp']}")
                    
                except json.JSONDecodeError:
                    print(f"   📝 Response: {response.text[:100]}...")
                    
            else:
                print(f"❌ {endpoint} - STATUS: {response.status_code}")
                print(f"   📝 Response: {response.text[:100]}...")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint} - ERROR: {e}")
    
    print(f"\n🎯 HEALTH ENDPOINT UNIFICATION TEST COMPLETE")

if __name__ == "__main__":
    test_health_endpoints()
