#!/usr/bin/env python3
"""
Lesson Flow Code Path Tracer
This script simulates the exact code path taken by a "start lesson" request
through the backend system, showing each function call and decision point.
"""

import sys
import os
import json
from datetime import datetime

# Add the backend path to sys.path
backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
sys.path.insert(0, backend_path)

class LessonFlowTracer:
    def __init__(self):
        self.trace_log = []
        self.current_state = {}
        
    def log_trace(self, step, function_name, input_data, output_data, line_number=None):
        """Log each step in the lesson flow"""
        trace_entry = {
            "step": step,
            "timestamp": datetime.now().isoformat(),
            "function": function_name,
            "line": line_number,
            "input": input_data,
            "output": output_data,
            "current_state": self.current_state.copy()
        }
        self.trace_log.append(trace_entry)
        print(f"TRACE {step}: {function_name} -> {output_data}")
        
    def simulate_lesson_start_flow(self):
        """Simulate the complete flow for a 'start lesson' request"""
        print("🔍 LESSON FLOW CODE PATH TRACER")
        print("Simulating: POST /api/enhance-content with 'start lesson' message")
        print("="*70)
        
        # Step 1: Request arrives at enhance_content_api
        self.log_trace(
            1, 
            "enhance_content_api (main.py:5312)",
            {
                "method": "POST",
                "endpoint": "/api/enhance-content",
                "message": "start lesson",
                "student_id": "test_student",
                "lesson_ref": "P5-COM-001"
            },
            "Request validation and parameter extraction"
        )
        
        # Step 2: Session state initialization
        initial_state = {
            "session_id": "new_session_123",
            "student_id": "test_student",
            "student_name": "Test Student",
            "current_lesson_phase": "diagnostic_start_probe",
            "current_phase": "diagnostic_start_probe",
            "current_probing_level_number": 5,
            "current_question_index": 0,
            "diagnostic_completed_this_session": False
        }
        
        self.current_state = initial_state
        self.log_trace(
            2,
            "get_or_initialize_lesson_state (main.py:1573)",
            {"is_new_session": True, "grade": "Primary 5"},
            initial_state,
            1573
        )
        
        # Step 3: Phase calculation for current phase
        current_phase = "diagnostic_start_probe"
        calculated_next_phase = self.simulate_calculate_next_mandatory_phase(current_phase)
        
        self.log_trace(
            3,
            "calculate_next_mandatory_phase (main.py:4097)",
            {"current_phase": current_phase, "request_id": "test_request_001"},
            {"next_phase": calculated_next_phase},
            4097
        )
        
        # Step 4: Context building for AI
        context_for_ai = {
            "python_calculated_new_phase_for_block": calculated_next_phase,
            "lesson_phase": current_phase,
            "student_name": "Test Student",
            "topic": "Introduction to Programming",
            "key_concepts_str": "variables, functions, loops",
            "current_probing_level_number": 5,
            "interaction_count": 1
        }
        
        self.log_trace(
            4,
            "AI Context Building (main.py:7380-7580)",
            {"current_phase": current_phase},
            context_for_ai
        )
        
        # Step 5: AI instruction template formatting
        formatted_rules = self.simulate_base_instructor_rules_formatting(context_for_ai)
        
        self.log_trace(
            5,
            "BASE_INSTRUCTOR_RULES.format() (main.py:2969)",
            context_for_ai,
            {"formatted_rules_length": len(formatted_rules), "contains_phase": calculated_next_phase in formatted_rules}
        )
        
        # Step 6: AI response generation
        ai_response = self.simulate_ai_response(current_phase, calculated_next_phase)
        
        self.log_trace(
            6,
            "generate_natural_ai_response (main.py:10482)",
            {"user_query": "start lesson", "context": context_for_ai},
            ai_response,
            10482
        )
        
        # Step 7: AI state block parsing
        parsed_state = self.simulate_ai_state_parsing(ai_response["response_text"])
        
        self.log_trace(
            7,
            "AI State Block Parsing (main.py:6050+)",
            {"ai_response": ai_response["response_text"]},
            parsed_state
        )
        
        # Step 8: State persistence to Firestore
        self.current_state.update(parsed_state)
        
        self.log_trace(
            8,
            "Firestore State Update (main.py:6200+)",
            {"session_id": "new_session_123", "updates": parsed_state},
            {"success": True, "new_phase": parsed_state.get("new_phase")}
        )
        
        # Step 9: Response formatting for frontend
        frontend_response = {
            "success": True,
            "message": "Content enhanced successfully",
            "data": {
                "enhanced_content": ai_response["content"],
                "current_phase": parsed_state.get("new_phase", current_phase),
                "diagnostic_complete": False,
                "lesson_complete": False
            }
        }
        
        self.log_trace(
            9,
            "Response Formatting for Frontend",
            self.current_state,
            frontend_response
        )
        
        print("\n" + "="*70)
        print("COMPLETE LESSON START FLOW TRACED")
        print("="*70)
        
        return self.trace_log
    
    def simulate_calculate_next_mandatory_phase(self, current_phase):
        """Simulate the phase calculation logic - EXACT copy from main.py"""
        import re
        
        try:
            # DIAGNOSTIC PHASES (8-step sequence)
            if current_phase.startswith('diagnostic_'):
                # Extract the level number (e.g., '5' from '..._L5_...')
                level_match = re.search(r'_L(\d+)_', current_phase)
                level_num = level_match.group(1) if level_match else "5" # Default to 5 if not found

                # --- Mandatory 8-Phase Diagnostic Progression Rules ---
                if current_phase == 'diagnostic_start_probe':
                    return f'diagnostic_probing_L{level_num}_ask_q1'
                
                # Handle intermediate evaluation/ask pairs (q1->q2, q2->q3, etc.)
                eval_match = re.search(r'_eval_q(\d+)_ask_q(\d+)', current_phase)
                if eval_match:
                    ask_q_num = int(eval_match.group(2))
                    if ask_q_num < 5:
                        return f'diagnostic_probing_L{level_num}_eval_q{ask_q_num}_ask_q{ask_q_num + 1}'
                    else:
                        return f'diagnostic_probing_L{level_num}_eval_q5_decide_level'

                # Handle transition from first question to first evaluation
                if current_phase.endswith('_ask_q1'):
                    return f'diagnostic_probing_L{level_num}_eval_q1_ask_q2'

                # Handle final decision step - transition to teaching
                if current_phase.endswith('_eval_q5_decide_level'):
                    return f'teaching_start_level_{level_num}'

            # TEACHING PHASES
            elif current_phase.startswith('teaching_start_level_'):
                # Always transition from teaching_start_level_X to teaching
                return 'teaching'
                
            elif current_phase == 'teaching':
                # After sufficient teaching interactions, transition to quiz
                return 'quiz_initiate'

            # QUIZ PHASES
            elif current_phase == 'quiz_initiate':
                # Always transition to quiz_questions after initiation
                return 'quiz_questions'
                
            elif current_phase == 'quiz_questions':
                # After all quiz questions are completed, show results
                return 'quiz_results'
                
            elif current_phase == 'quiz_results':
                # After showing results, move to conclusion
                return 'conclusion_summary'

            # CONCLUSION AND COMPLETION PHASES
            elif current_phase == 'conclusion_summary':
                # After summary, move to final assessment
                return 'final_assessment_pending'
                
            elif current_phase == 'final_assessment_pending':
                # Complete the lesson
                return 'completed'
                
            elif current_phase == 'completed':
                # Lesson is complete - stay completed
                return 'completed'

            # FALLBACK FOR UNKNOWN PHASES
            else:
                print(f"WARNING: Unhandled phase '{current_phase}'. Returning current phase.")
                return current_phase

        except Exception as e:
            print(f"ERROR in simulate_calculate_next_mandatory_phase: {e}")
            return current_phase # Fallback to the current phase on error
    
    def simulate_base_instructor_rules_formatting(self, context):
        """Simulate the instructor rules template formatting"""
        template_excerpt = """
        CRITICAL PHASE PROGRESSION: 
        • The system has calculated the next phase as: {python_calculated_new_phase_for_block}
        • You MUST use this exact phase name in your state update block
        
        You are {student_name}'s expert tutor.
        Current Phase: {lesson_phase}
        Topic: {topic}
        """
        return template_excerpt.format(**context)
    
    def simulate_ai_response(self, current_phase, next_phase):
        """Simulate AI response generation"""
        if current_phase == "diagnostic_start_probe":
            content = f"Hello Test Student! Welcome to our lesson on Introduction to Programming. I'm here to help you learn about variables, functions, and loops. Are you ready to start with some diagnostic questions to understand your current level?"
            state_block = f'// AI_STATE_UPDATE_BLOCK_START {{"new_phase": "{next_phase}", "interaction_count": 1}} // AI_STATE_UPDATE_BLOCK_END'
            response_text = f"{content}\n\n{state_block}"
            
            return {
                "content": content,
                "response_text": response_text,
                "state_block": state_block
            }
        return {"content": "Default response", "response_text": "Default response", "state_block": ""}
    
    def simulate_ai_state_parsing(self, response_text):
        """Simulate parsing the AI state update block"""
        import re
        
        # Look for AI_STATE_UPDATE_BLOCK
        pattern = r'// AI_STATE_UPDATE_BLOCK_START ({.*?}) // AI_STATE_UPDATE_BLOCK_END'
        match = re.search(pattern, response_text)
        
        if match:
            try:
                state_json = match.group(1)
                parsed_state = json.loads(state_json)
                return parsed_state
            except:
                return {"new_phase": "diagnostic_start_probe"}  # Fallback
        
        return {"new_phase": "diagnostic_start_probe"}  # Default
    
    def trace_full_lesson_progression(self):
        """Trace the complete lesson from start to completion"""
        print("\n🎯 FULL LESSON PROGRESSION TRACE")
        print("="*70)
        
        phases = [
            "diagnostic_start_probe",
            "diagnostic_probing_L5_ask_q1", 
            "diagnostic_probing_L5_eval_q1_ask_q2",
            "diagnostic_probing_L5_eval_q2_ask_q3",
            "diagnostic_probing_L5_eval_q3_ask_q4", 
            "diagnostic_probing_L5_eval_q4_ask_q5",
            "diagnostic_probing_L5_eval_q5_decide_level",
            "teaching_start_level_5",
            "teaching",
            "quiz_initiate",
            "quiz_questions", 
            "quiz_results",
            "conclusion_summary",
            "final_assessment_pending",
            "completed"
        ]
        
        for i, phase in enumerate(phases):
            next_phase = phases[i + 1] if i < len(phases) - 1 else "completed"
            calculated_next = self.simulate_calculate_next_mandatory_phase(phase)
            
            print(f"{i+1:2d}. {phase}")
            print(f"    Next: {calculated_next}")
            print(f"    Expected: {next_phase}")
            print(f"    Match: {'✓' if calculated_next == next_phase else '✗'}")
            print()
    
    def generate_trace_report(self):
        """Generate a detailed trace report"""
        report_filename = f"lesson_flow_trace_report_{int(datetime.now().timestamp())}.json"
        
        report_data = {
            "trace_summary": {
                "total_steps": len(self.trace_log),
                "start_time": self.trace_log[0]["timestamp"] if self.trace_log else None,
                "end_time": self.trace_log[-1]["timestamp"] if self.trace_log else None,
                "final_state": self.current_state
            },
            "detailed_trace": self.trace_log
        }
        
        with open(report_filename, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed trace report saved to: {report_filename}")
        return report_filename

def main():
    """Main execution"""
    tracer = LessonFlowTracer()
    
    # Trace the lesson start flow
    trace_log = tracer.simulate_lesson_start_flow()
    
    # Trace the full lesson progression
    tracer.trace_full_lesson_progression()
    
    # Generate detailed report
    tracer.generate_trace_report()
    
    print("\n🎉 Lesson flow code path tracing complete!")
    print("Use this trace to compare against your frontend UI testing results.")

if __name__ == "__main__":
    main()
