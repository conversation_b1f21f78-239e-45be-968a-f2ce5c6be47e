#!/usr/bin/env python3
"""
Diagnostic test for phase progression issue
"""

import sys
import os

# Add the current directory to the path so we can import from main.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Test the phase calculation function directly
def test_phase_calculation():
    print("Testing calculate_next_mandatory_phase function...")
    
    try:
        # Import the function from main.py
        from main import calculate_next_mandatory_phase
        
        # Test the problematic transition
        current_phase = "diagnostic_start_probe"
        request_id = "test_request"
        
        next_phase = calculate_next_mandatory_phase(current_phase, request_id)
        
        print(f"Input phase: {current_phase}")
        print(f"Calculated next phase: {next_phase}")
        print(f"Expected: diagnostic_probing_L5_ask_q1")
        
        if next_phase == "diagnostic_probing_L5_ask_q1":
            print("✅ Phase calculation is working correctly!")
            return True
        else:
            print("❌ Phase calculation is NOT working correctly!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing phase calculation: {e}")
        return False

def test_initial_state_setup():
    print("\nTesting initial state setup...")
    
    try:
        from main import get_or_initialize_lesson_state, LESSON_PHASE_DIAGNOSTIC
        
        # Test parameters
        session_id = "test_session_123"
        student_id = "test_student"
        student_name = "Test Student"
        lesson_data = {
            "grade": "Primary 5",
            "subject": "Computing",
            "key_concepts": ["chatbots", "programming"],
            "lessonRef": "P5-COM-001"
        }
        
        # Create initial state
        initial_state = get_or_initialize_lesson_state(
            session_id, student_id, student_name, lesson_data, is_new_session=True
        )
        
        print(f"Initial state phase: {initial_state.get('current_phase')}")
        print(f"Initial lesson phase: {initial_state.get('current_lesson_phase')}")
        print(f"Expected: {LESSON_PHASE_DIAGNOSTIC}")
        
        if initial_state.get('current_phase') == LESSON_PHASE_DIAGNOSTIC:
            print("✅ Initial state setup is working correctly!")
            return True
        else:
            print("❌ Initial state setup is NOT working correctly!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing initial state: {e}")
        return False

if __name__ == "__main__":
    print("=== DIAGNOSTIC PHASE PROGRESSION TEST ===\n")
    
    # Test individual components
    calc_success = test_phase_calculation()
    state_success = test_initial_state_setup()
    
    print(f"\n=== RESULTS ===")
    print(f"Phase calculation: {'✅' if calc_success else '❌'}")
    print(f"Initial state: {'✅' if state_success else '❌'}")
    
    if calc_success and state_success:
        print("\n🔍 Both components are working. The issue might be in:")
        print("1. AI prompt generation")
        print("2. AI state block parsing")
        print("3. State persistence to Firestore")
        print("4. State retrieval from Firestore")
    else:
        print("\n🔧 Found issues in core components that need fixing!")
