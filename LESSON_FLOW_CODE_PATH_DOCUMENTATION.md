# SOLYNTA LESSON FLOW - COMPLETE CODE PATH DOCUMENTATION

## Overview
This document traces the exact code path taken from a "start lesson" trigger through the complete lesson progression from `diagnostic_start_probe` to `completed` phase.

## Entry Point: `/api/enhance-content` Endpoint

### 1. Initial Request Processing
**File**: `main.py` Lines 5312-5400
**Function**: `enhance_content_api(decoded_token)`

```python
@app.route('/api/enhance-content', methods=['POST', 'OPTIONS'], endpoint='enhance_content_api')
@require_auth
@check_role_access  
@performance_monitor
@wrap_async
async def enhance_content_api(decoded_token):
```

**Key Actions**:
- Request validation and parameter extraction
- Student profile retrieval
- Lesson data fetching
- Session management

### 2. Session State Initialization
**File**: `main.py` Lines 1573-1650
**Function**: `get_or_initialize_lesson_state()`

**Key State Initialization**:
```python
initial_state_dict = {
    "session_id": session_id_from_request,
    "student_id": student_id,
    "student_name": student_name,
    "current_lesson_phase": "diagnostic_start_probe",  # ALWAYS starts here
    "current_phase": "diagnostic_start_probe",         # Duplicate for consistency
    "current_probing_level_number": initial_probing_level,
    "current_question_index": 0,                       # Always start from 0
    "diagnostic_completed_this_session": False,
    # ... other fields
}
```

### 3. Phase Calculation and Context Building
**File**: `main.py` Lines 7420-7480
**Function**: Phase-specific logic in `enhance_content_api`

**For diagnostic_start_probe phase**:
```python
# STRICT SEQUENCE ENFORCEMENT: Calculate next mandatory phase
python_calculated_new_phase_for_block = calculate_next_mandatory_phase(lesson_phase_from_context, request_id)
```

## Core Phase Calculation Logic

### 4. calculate_next_mandatory_phase() Function
**File**: `main.py` Lines 4097-4200
**Function**: `calculate_next_mandatory_phase(current_phase: str, request_id: str)`

**Complete Phase Progression Logic**:

#### A. Diagnostic Phase Sequence (8 mandatory steps):
```python
if current_phase.startswith('diagnostic_'):
    level_match = re.search(r'_L(\d+)_', current_phase)
    level_num = level_match.group(1) if level_match else "5"
    
    if current_phase == 'diagnostic_start_probe':
        return f'diagnostic_probing_L{level_num}_ask_q1'
    
    if current_phase.endswith('_ask_q1'):
        return f'diagnostic_probing_L{level_num}_eval_q1_ask_q2'
    
    # Pattern: eval_q{N}_ask_q{N+1}
    eval_match = re.search(r'_eval_q(\d+)_ask_q(\d+)', current_phase)
    if eval_match:
        ask_q_num = int(eval_match.group(2))
        if ask_q_num < 5:
            return f'diagnostic_probing_L{level_num}_eval_q{ask_q_num}_ask_q{ask_q_num + 1}'
        else:
            return f'diagnostic_probing_L{level_num}_eval_q5_decide_level'
    
    if current_phase.endswith('_eval_q5_decide_level'):
        return f'teaching_start_level_{level_num}'
```

#### B. Teaching Phase Progression:
```python
elif current_phase.startswith('teaching_start_level_'):
    return 'teaching'
    
elif current_phase == 'teaching':
    return 'quiz_initiate'
```

#### C. Quiz Phase Progression:
```python
elif current_phase == 'quiz_initiate':
    return 'quiz_questions'
    
elif current_phase == 'quiz_questions':
    return 'quiz_results'
    
elif current_phase == 'quiz_results':
    return 'conclusion_summary'
```

#### D. Completion Phase Progression:
```python
elif current_phase == 'conclusion_summary':
    return 'final_assessment_pending'
    
elif current_phase == 'final_assessment_pending':
    return 'completed'
    
elif current_phase == 'completed':
    return 'completed'  # Stay completed
```

## AI Instruction and Response Generation

### 5. AI Context Building
**File**: `main.py` Lines 7380-7580
**Key Context Variables**:
```python
context_for_enhance = {
    'python_calculated_new_phase_for_block': python_calculated_new_phase_for_block,
    'lesson_phase': lesson_phase_from_context,
    'student_name': student_name,
    'topic': topic,
    'key_concepts_str': key_concepts_str_ctx,
    'current_probing_level_number': current_probing_level_number_from_state,
    # ... many other fields
}
```

### 6. AI Response Generation
**File**: `main.py` Lines 10482-10580
**Function**: `generate_natural_ai_response()`

**Key Steps**:
1. Format BASE_INSTRUCTOR_RULES template with context
2. Call AI model with formatted prompt
3. Return raw AI response text (including state block)

### 7. AI State Block Parsing and Enforcement
**File**: `main.py` Lines 6050-6200
**In enhance_content_api flow**:

```python
enhanced_content_text, state_updates_from_ai, raw_ai_state_block_str_for_return = await generate_natural_ai_response(...)

# Parse AI state updates
current_phase = state_updates_from_ai.get('new_phase') or current_phase_for_ai

# Log phase transitions
if new_phase and new_phase != current_phase_for_ai:
    logger.warning(f"[{request_id}] 🔄 PHASE TRANSITION: {current_phase_for_ai} → {new_phase}")
```

## Complete Lesson Flow Progression

### Phase Sequence with Triggers:

1. **diagnostic_start_probe** 
   - Trigger: Initial lesson start
   - Next: `diagnostic_probing_L5_ask_q1`

2. **diagnostic_probing_L5_ask_q1**
   - Trigger: Student response to introduction
   - Next: `diagnostic_probing_L5_eval_q1_ask_q2`

3. **diagnostic_probing_L5_eval_q1_ask_q2**
   - Trigger: Student answers Q1
   - Next: `diagnostic_probing_L5_eval_q2_ask_q3`

4. **diagnostic_probing_L5_eval_q2_ask_q3**
   - Trigger: Student answers Q2
   - Next: `diagnostic_probing_L5_eval_q3_ask_q4`

5. **diagnostic_probing_L5_eval_q3_ask_q4**
   - Trigger: Student answers Q3
   - Next: `diagnostic_probing_L5_eval_q4_ask_q5`

6. **diagnostic_probing_L5_eval_q4_ask_q5**
   - Trigger: Student answers Q4
   - Next: `diagnostic_probing_L5_eval_q5_decide_level`

7. **diagnostic_probing_L5_eval_q5_decide_level**
   - Trigger: Student answers Q5
   - Next: `teaching_start_level_5`

8. **teaching_start_level_5**
   - Trigger: Diagnostic completion
   - Next: `teaching`

9. **teaching**
   - Trigger: Teaching introduction complete
   - Next: `quiz_initiate` (after sufficient interactions)

10. **quiz_initiate**
    - Trigger: Teaching phase complete
    - Next: `quiz_questions`

11. **quiz_questions**
    - Trigger: Quiz introduction complete
    - Next: `quiz_results` (after all questions answered)

12. **quiz_results**
    - Trigger: All quiz questions completed
    - Next: `conclusion_summary`

13. **conclusion_summary**
    - Trigger: Quiz feedback provided
    - Next: `final_assessment_pending`

14. **final_assessment_pending**
    - Trigger: Summary complete
    - Next: `completed`

15. **completed**
    - Trigger: Final assessment complete
    - Next: `completed` (stable end state)

## State Persistence

### 8. Firestore State Updates
**File**: `main.py` Lines 6200+
**Collections Used**:
- `lesson_states` - Current session state
- `lesson_sessions` - Session metadata

**State Update Pattern**:
```python
session_state_ref = db.collection(FS_COLLECTION_LESSON_STATES).document(session_id)
session_state_ref.set({
    'current_phase': final_phase_to_save,
    'current_lesson_phase': final_phase_to_save,
    # ... other state updates
}, merge=True)
```

## AI Instruction Template Integration

### 9. BASE_INSTRUCTOR_RULES Template
**File**: `main.py` Lines 2969-3200+

**Key Phase-Specific Instructions**:
- Each phase has specific behavioral instructions
- Mandatory state update block requirements
- Progressive transition enforcement
- Student help response protocols

**Template Variables**:
```python
{
    'python_calculated_new_phase_for_block': python_calculated_new_phase_for_block,
    'lesson_phase': current_phase,
    'student_name': student_name,
    'topic': topic,
    'key_concepts_str': key_concepts_str,
    # ... many others
}
```

## Critical Enforcement Points

### 10. State Block Validation
**Function**: `validate_ai_state_update()` (referenced but not detailed)
- Validates AI-generated state transitions
- Prevents backward phase transitions
- Ensures phase progression consistency

### 11. Emergency Safety Nets
**File**: `main.py` Lines 6100-6150
- Prevents sessions from getting stuck
- Forces progression after excessive interactions
- Maintains lesson flow integrity

## Frontend Integration Points

### 12. Response Format
**Return Structure**:
```json
{
    "success": true,
    "message": "Content enhanced successfully",
    "data": {
        "enhanced_content": "AI response text",
        "current_phase": "new_phase_name",
        "lesson_complete": false,
        "diagnostic_complete": false,
        // ... other state fields
    }
}
```

---

## Comparison Notes for Frontend Testing

When comparing frontend behavior to this backend flow:

1. **Initial Phase**: Frontend should start with `diagnostic_start_probe`
2. **Phase Transitions**: Each user interaction should trigger exactly one phase progression
3. **State Consistency**: Frontend phase should match backend `current_phase` field
4. **Mandatory Progression**: No skipping of intermediate phases allowed
5. **Completion Tracking**: Each major section (diagnostic, teaching, quiz) has completion flags

This documentation provides the complete code path for comparing frontend behavior against the expected backend lesson progression logic.
