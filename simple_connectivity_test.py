#!/usr/bin/env python3
"""
Simple Backend Connectivity Test
"""

import requests
import sys

def test_backend_connectivity():
    """Test if the backend server is running and accessible."""
    
    print("🔌 BACKEND CONNECTIVITY TEST")
    print("=" * 40)
    
    base_url = "http://localhost:5000"
    
    # Test health endpoint
    try:
        print("🧪 Testing health endpoint...")
        response = requests.get(f"{base_url}/health", timeout=5)
        
        if response.status_code == 200:
            print("✅ Backend server is running and accessible!")
            print(f"   Health check: {response.status_code}")
            return True
        else:
            print(f"⚠️ Health check returned: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is not running")
        print("💡 Start it with: python backend/cloud_function/lesson_manager/main.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ Backend server timeout")
        return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def test_simple_api_call():
    """Test a simple API call to see if our fixes work."""
    
    print("\n🧪 SIMPLE API TEST")
    print("=" * 40)
    
    # Simple test request
    test_data = {
        "session_id": "test_session_123",
        "student_id": "test_student",
        "lesson_ref": "P5-COM-001",
        "content_to_enhance": "Start lesson",
        "grade": "Primary 5",
        "subject": "Computing",
        "level": "5",
        "student_info": {"first_name": "TestStudent"},
        "chat_history": []
    }
    
    try:
        print("🚀 Making test API call...")
        response = requests.post("http://localhost:5000/api/enhance-content", json=test_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            current_phase = result.get('data', {}).get('current_phase', 'Unknown')
            
            print(f"✅ API call successful!")
            print(f"📊 Current phase: {current_phase}")
            
            if current_phase == "diagnostic_probing_L5_ask_q1":
                print("🎉 FRONTEND FIX WORKING!")
                print("   diagnostic_start_probe → diagnostic_probing_L5_ask_q1")
                return True
            elif current_phase == "diagnostic_start_probe":
                print("❌ Still stuck in diagnostic_start_probe")
                return False
            else:
                print(f"❓ Unexpected phase: {current_phase}")
                return False
                
        else:
            print(f"❌ API call failed: {response.status_code}")
            print(f"Error: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ API test error: {e}")
        return False

if __name__ == "__main__":
    print("Testing backend connectivity and basic API functionality...\n")
    
    # Test connectivity
    if test_backend_connectivity():
        # If backend is running, test the API
        api_success = test_simple_api_call()
        
        print(f"\n🏁 CONNECTIVITY TEST COMPLETED")
        if api_success:
            print("🎉 FRONTEND FIX IS WORKING!")
            print("The diagnostic_start_probe progression issue is resolved.")
        else:
            print("🔧 API working but phase progression needs more debugging.")
    else:
        print(f"\n🏁 CONNECTIVITY TEST COMPLETED")
        print("❌ Backend server not accessible")
        print("Start the server first, then run this test again.")
    
    print(f"\nNext: Run full end-to-end test with: python frontend_e2e_test.py")
