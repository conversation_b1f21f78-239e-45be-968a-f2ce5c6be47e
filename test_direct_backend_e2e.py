#!/usr/bin/env python3
"""
Direct Backend End-to-End Lesson Flow Test
Tests the complete lesson progression logic directly against backend functions.
This bypasses HTTP and tests the core lesson management logic.
"""

import sys
import os
import json
import time
from datetime import datetime

# Add the backend path to sys.path
backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
sys.path.insert(0, backend_path)

try:
    from main import (
        calculate_next_mandatory_phase,
        enhance_content_api,
        generate_natural_ai_response,
        BASE_INSTRUCTOR_RULES
    )
    print("✓ Successfully imported main functions")
except ImportError as e:
    print(f"✗ Import error: {e}")
    sys.exit(1)

class DirectBackendLessonTest:
    def __init__(self):
        self.test_results = []
        self.current_phase = "diagnostic"
        self.lesson_state = {
            "current_phase": "diagnostic",
            "lesson_id": "test_lesson_001",
            "user_id": "test_user",
            "diagnostic_complete": False,
            "teaching_complete": False,
            "quiz_complete": False,
            "conclusion_complete": False,
            "completed": False,
            "phase_history": []
        }
        
    def log_test(self, test_name, passed, details=""):
        """Log test result"""
        result = {
            "test": test_name,
            "passed": passed,
            "details": details,
            "timestamp": datetime.now().isoformat(),
            "current_phase": self.current_phase
        }
        self.test_results.append(result)
        status = "✓" if passed else "✗"
        print(f"{status} {test_name}: {details}")
        
    def test_phase_calculation(self, current_phase, expected_next_phase, state_overrides=None):
        """Test phase calculation logic"""
        try:
            test_state = self.lesson_state.copy()
            if state_overrides:
                test_state.update(state_overrides)
                
            calculated_phase = calculate_next_mandatory_phase(current_phase, test_state)
            passed = calculated_phase == expected_next_phase
            self.log_test(
                f"Phase calculation: {current_phase} -> {expected_next_phase}",
                passed,
                f"Got: {calculated_phase}, Expected: {expected_next_phase}"
            )
            return passed
        except Exception as e:
            self.log_test(
                f"Phase calculation: {current_phase} -> {expected_next_phase}",
                False,
                f"Exception: {str(e)}"
            )
            return False
    
    def test_ai_response_with_enforcement(self, phase, user_message, expected_phase_transition=None):
        """Test AI response generation with enforcement"""
        try:
            # Update current phase in lesson state
            test_state = self.lesson_state.copy()
            test_state["current_phase"] = phase
            
            # Create a mock request body
            request_body = {
                "message": user_message,
                "lessonState": test_state
            }
            
            # Test the enhanced content API
            result = enhance_content_api(request_body)
            
            # Parse the result
            if isinstance(result, str):
                response_data = json.loads(result)
            else:
                response_data = result
            
            # Check if the response contains the expected elements
            has_ai_response = "aiResponse" in response_data
            has_lesson_state = "lessonState" in response_data
            
            if has_lesson_state and expected_phase_transition:
                new_phase = response_data["lessonState"].get("current_phase")
                phase_correct = new_phase == expected_phase_transition
            else:
                phase_correct = True
            
            passed = has_ai_response and has_lesson_state and phase_correct
            
            details = f"AI Response: {has_ai_response}, Lesson State: {has_lesson_state}"
            if expected_phase_transition:
                actual_phase = response_data.get("lessonState", {}).get("current_phase", "unknown")
                details += f", Phase: {actual_phase} (expected: {expected_phase_transition})"
            
            self.log_test(
                f"AI Response for {phase} phase",
                passed,
                details
            )
            
            # Update lesson state if successful
            if has_lesson_state:
                self.lesson_state.update(response_data["lessonState"])
                self.current_phase = self.lesson_state.get("current_phase", self.current_phase)
            
            return passed, response_data
            
        except Exception as e:
            self.log_test(
                f"AI Response for {phase} phase",
                False,
                f"Exception: {str(e)}"
            )
            return False, None
    
    def test_all_phase_transitions(self):
        """Test all expected phase transitions in sequence"""
        print("\n" + "="*60)
        print("TESTING ALL PHASE TRANSITIONS")
        print("="*60)
        
        # Define the expected phase progression
        phase_transitions = [
            ("diagnostic", "diagnostic", {}),
            ("diagnostic", "teaching_start", {"diagnostic_complete": True}),
            ("teaching_start", "teaching", {}),
            ("teaching", "quiz_initiate", {"teaching_complete": True}),
            ("quiz_initiate", "quiz_questions", {}),
            ("quiz_questions", "quiz_results", {"quiz_complete": True}),
            ("quiz_results", "conclusion_summary", {}),
            ("conclusion_summary", "final_assessment_pending", {"conclusion_complete": True}),
            ("final_assessment_pending", "completed", {}),
            ("completed", "completed", {})  # Should stay completed
        ]
        
        for current_phase, expected_next, state_overrides in phase_transitions:
            self.test_phase_calculation(current_phase, expected_next, state_overrides)
    
    def simulate_complete_lesson_flow(self):
        """Simulate a complete lesson from start to finish"""
        print("\n" + "="*60)
        print("COMPREHENSIVE END-TO-END LESSON FLOW SIMULATION")
        print("="*60)
        
        # Reset state for clean test
        self.lesson_state = {
            "current_phase": "diagnostic",
            "lesson_id": "test_lesson_001",
            "user_id": "test_user",
            "diagnostic_complete": False,
            "teaching_complete": False,
            "quiz_complete": False,
            "conclusion_complete": False,
            "completed": False,
            "phase_history": []
        }
        
        # Phase 1: Initial Diagnostic
        print(f"\n--- Phase 1: Diagnostic ---")
        success, response = self.test_ai_response_with_enforcement(
            "diagnostic",
            "I'm ready to start the lesson",
            "diagnostic"
        )
        
        # Simulate completing diagnostic
        self.lesson_state.update({
            "diagnostic_complete": True,
            "current_phase": "diagnostic"
        })
        
        # Phase 2: Teaching Start
        print(f"\n--- Phase 2: Teaching Start ---")
        # Test transition calculation first
        self.test_phase_calculation("diagnostic", "teaching_start", {"diagnostic_complete": True})
        
        success, response = self.test_ai_response_with_enforcement(
            "teaching_start",
            "I've completed the diagnostic. Let's start learning!",
            "teaching_start"
        )
        
        self.lesson_state.update({"current_phase": "teaching_start"})
        
        # Phase 3: Teaching
        print(f"\n--- Phase 3: Teaching ---")
        self.test_phase_calculation("teaching_start", "teaching")
        
        success, response = self.test_ai_response_with_enforcement(
            "teaching",
            "Please teach me about this topic",
            "teaching"
        )
        
        # Simulate completing teaching
        self.lesson_state.update({
            "current_phase": "teaching",
            "teaching_complete": True
        })
        
        # Phase 4: Quiz Initiate
        print(f"\n--- Phase 4: Quiz Initiate ---")
        self.test_phase_calculation("teaching", "quiz_initiate", {"teaching_complete": True})
        
        success, response = self.test_ai_response_with_enforcement(
            "quiz_initiate",
            "I'm ready for the quiz now",
            "quiz_initiate"
        )
        
        self.lesson_state.update({"current_phase": "quiz_initiate"})
        
        # Phase 5: Quiz Questions
        print(f"\n--- Phase 5: Quiz Questions ---")
        self.test_phase_calculation("quiz_initiate", "quiz_questions")
        
        success, response = self.test_ai_response_with_enforcement(
            "quiz_questions",
            "My answer is option A",
            "quiz_questions"
        )
        
        # Simulate completing quiz
        self.lesson_state.update({
            "current_phase": "quiz_questions",
            "quiz_complete": True
        })
        
        # Phase 6: Quiz Results
        print(f"\n--- Phase 6: Quiz Results ---")
        self.test_phase_calculation("quiz_questions", "quiz_results", {"quiz_complete": True})
        
        success, response = self.test_ai_response_with_enforcement(
            "quiz_results",
            "How did I do on the quiz?",
            "quiz_results"
        )
        
        self.lesson_state.update({"current_phase": "quiz_results"})
        
        # Phase 7: Conclusion Summary
        print(f"\n--- Phase 7: Conclusion Summary ---")
        self.test_phase_calculation("quiz_results", "conclusion_summary")
        
        success, response = self.test_ai_response_with_enforcement(
            "conclusion_summary",
            "Please summarize what I learned",
            "conclusion_summary"
        )
        
        # Simulate completing conclusion
        self.lesson_state.update({
            "current_phase": "conclusion_summary",
            "conclusion_complete": True
        })
        
        # Phase 8: Final Assessment Pending
        print(f"\n--- Phase 8: Final Assessment Pending ---")
        self.test_phase_calculation("conclusion_summary", "final_assessment_pending", {"conclusion_complete": True})
        
        success, response = self.test_ai_response_with_enforcement(
            "final_assessment_pending",
            "I'm ready for the final assessment",
            "final_assessment_pending"
        )
        
        self.lesson_state.update({"current_phase": "final_assessment_pending"})
        
        # Phase 9: Completed
        print(f"\n--- Phase 9: Completed ---")
        self.test_phase_calculation("final_assessment_pending", "completed")
        
        success, response = self.test_ai_response_with_enforcement(
            "completed",
            "Thank you for the lesson",
            "completed"
        )
        
        # Final state
        self.lesson_state.update({
            "current_phase": "completed",
            "completed": True
        })
        
        # Verify completed state is stable
        print(f"\n--- Verifying Completed State Stability ---")
        self.test_phase_calculation("completed", "completed", {"completed": True})
        
    def test_edge_cases(self):
        """Test edge cases and error handling"""
        print(f"\n--- Testing Edge Cases ---")
        
        # Test 1: Invalid phase handling
        try:
            result = calculate_next_mandatory_phase("invalid_phase", self.lesson_state)
            self.log_test(
                "Invalid phase handling",
                result == "diagnostic",  # Should default to diagnostic
                f"Got: {result}"
            )
        except Exception as e:
            self.log_test(
                "Invalid phase handling",
                False,
                f"Exception: {str(e)}"
            )
        
        # Test 2: Empty lesson state
        try:
            result = calculate_next_mandatory_phase("diagnostic", {})
            self.log_test(
                "Empty state handling",
                result == "diagnostic",
                f"Got: {result}"
            )
        except Exception as e:
            self.log_test(
                "Empty state handling",
                False,
                f"Exception: {str(e)}"
            )
        
        # Test 3: Backward phase prevention
        completed_state = {"completed": True, "current_phase": "completed"}
        result = calculate_next_mandatory_phase("teaching", completed_state)
        self.log_test(
            "Backward phase prevention",
            result != "teaching",  # Should not go backward
            f"Got: {result} from completed state"
        )
        
        # Test 4: Missing completion flags
        incomplete_state = {"current_phase": "quiz_results"}  # Missing quiz_complete
        result = calculate_next_mandatory_phase("quiz_results", incomplete_state)
        self.log_test(
            "Missing completion flags handling",
            result == "quiz_results",  # Should stay in current phase
            f"Got: {result}"
        )
    
    def test_instructor_rules_coverage(self):
        """Test that instructor rules cover all phases"""
        print(f"\n--- Testing Instructor Rules Coverage ---")
        
        expected_phases = [
            "diagnostic", "teaching_start", "teaching", "quiz_initiate", 
            "quiz_questions", "quiz_results", "conclusion_summary", 
            "final_assessment_pending", "completed"
        ]
        
        for phase in expected_phases:
            # Check if BASE_INSTRUCTOR_RULES contains phase-specific instructions
            phase_in_rules = phase.replace("_", " ") in BASE_INSTRUCTOR_RULES.lower()
            self.log_test(
                f"Instructor rules for {phase}",
                phase_in_rules,
                f"Phase {'found' if phase_in_rules else 'missing'} in instructor rules"
            )
    
    def generate_report(self):
        """Generate final test report"""
        print("\n" + "="*60)
        print("FINAL TEST REPORT")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["passed"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\nFailed Tests:")
            for result in self.test_results:
                if not result["passed"]:
                    print(f"  - {result['test']}: {result['details']}")
        
        # Save detailed report
        report_filename = f"direct_backend_e2e_test_report_{int(time.time())}.json"
        with open(report_filename, 'w') as f:
            json.dump({
                "summary": {
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "success_rate": (passed_tests/total_tests)*100,
                    "timestamp": datetime.now().isoformat()
                },
                "final_lesson_state": self.lesson_state,
                "detailed_results": self.test_results
            }, f, indent=2)
        
        print(f"\nDetailed report saved to: {report_filename}")
        
        return passed_tests == total_tests

def main():
    """Main test execution"""
    print("🧪 Direct Backend End-to-End Lesson Flow Test")
    print("Testing lesson progression logic directly against backend functions")
    print("="*70)
    
    test_suite = DirectBackendLessonTest()
    
    # Test 1: All phase transitions
    test_suite.test_all_phase_transitions()
    
    # Test 2: Complete lesson flow simulation
    test_suite.simulate_complete_lesson_flow()
    
    # Test 3: Edge cases
    test_suite.test_edge_cases()
    
    # Test 4: Instructor rules coverage
    test_suite.test_instructor_rules_coverage()
    
    # Generate final report
    all_passed = test_suite.generate_report()
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED! The lesson flow backend logic is working correctly.")
        print("✅ Ready for production use!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED. Check the report for details.")
        print("🔧 Backend logic needs fixes before production.")
        sys.exit(1)

if __name__ == "__main__":
    main()
