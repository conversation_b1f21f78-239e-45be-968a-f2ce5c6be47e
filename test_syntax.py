#!/usr/bin/env python3
"""
Simple test to verify the main.py syntax and imports work
"""
import sys
import os

# Add the lesson manager directory to path
sys.path.insert(0, r'C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager')

try:
    print("🧪 Testing Python syntax of main.py...")
    
    # Try to compile the file first
    with open(r'C:\Users\<USER>\OneDrive\Desktop\Desktop\Solynta_Website\backend\cloud_function\lesson_manager\main.py', 'r', encoding='utf-8') as f:
        code = f.read()
    
    compile(code, 'main.py', 'exec')
    print("✅ Syntax check passed!")
    
    # Try to import (this will check for runtime errors)
    print("🔄 Testing imports...")
    import main
    print("✅ Import successful!")
    
    print("🎉 SYNTAX ERROR IS FIXED!")
    print("The backend should now start without errors.")
    
except SyntaxError as e:
    print(f"❌ Syntax Error still exists:")
    print(f"   Line {e.lineno}: {e.text}")
    print(f"   Error: {e.msg}")
    
except Exception as e:
    print(f"⚠️ Import error (may be due to missing dependencies): {e}")
    print("✅ But the syntax is valid!")
