#!/usr/bin/env python3
"""
Minimal test to identify Python syntax error
"""

# Let's try to compile just a small section around the problematic area
test_code = '''
def _assess_answer_effort(student_answer: str) -> bool:
    """
    Assess if student has made substantial effort in their answer.
    Used for open-ended questions where exact matching isn't appropriate.
    """
    if not student_answer or len(student_answer.strip()) < 10:
        return False
    
    words = student_answer.strip().split()
    
    # Basic effort criteria
    has_min_length = len(words) >= 5
    has_meaningful_content = len([w for w in words if len(w) > 3]) >= 3
    has_punctuation = any(char in student_answer for char in '.!?,:;')
    
    # Avoid very short or single-word answers unless they're clearly correct
    if len(words) == 1:
        return len(student_answer.strip()) > 5
    
    # Check for thoughtful response indicators
    thoughtful_indicators = [
        'because', 'therefore', 'however', 'although', 'since', 'while',
        'example', 'such as', 'for instance', 'in other words', 'specifically'
    ]
    has_thoughtful_language = any(indicator in student_answer.lower() 
                                 for indicator in thoughtful_indicators)
    
    # Award credit for substantial, thoughtful responses
    effort_score = sum([has_min_length, has_meaningful_content, has_punctuation, has_thoughtful_language])
    
    # logger.info(f"Effort assessment: Length={len(words)} words, Score={effort_score}/4")
    return effort_score >= 2  # At least 2 out of 4 effort criteria

async def generate_natural_ai_response(
    user_query: str,
    chat_history,
    context,
    request_id: str,
    model
):
    """
    Generates natural AI responses. This function now focuses ONLY on formatting the prompt
    and calling the AI. State progression is handled by the calling function.
    """
    try:
        # --- Build the AI Prompt ---
        student_name = context.get('student_name', 'Student')
        
        # Use the pre-calculated phase from the context for the prompt
        # This is CRITICAL for giving the AI the correct instructions
        context['lesson_phase'] = context.get('python_calculated_new_phase_for_block', context.get('lesson_phase', 'diagnostic_start_probe'))
        
        return "test", {}, ""

    except Exception as e:
        student_name = context.get('student_name', 'Student')
        fallback_response = f"I'm here to help you with your studies, {student_name}! Could you please repeat your question?"
        return fallback_response, {}, ""
'''

import ast

try:
    ast.parse(test_code)
    print("✅ Test code syntax is valid")
except SyntaxError as e:
    print(f"❌ Syntax error in test code:")
    print(f"   Line {e.lineno}: {e.msg}")
    print(f"   Text: {e.text}")
