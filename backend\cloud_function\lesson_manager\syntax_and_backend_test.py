#!/usr/bin/env python3
"""Simple test to verify our fixes and backend connectivity."""

import requests
import sys

def test_backend():
    print("Testing backend connectivity...")
    
    try:
        response = requests.get("http://localhost:5000/api/health", timeout=5)
        print(f"Health check status: {response.status_code}")
        print(f"Response: {response.text}")
        return True
    except Exception as e:
        print(f"Backend not accessible: {e}")
        return False

def test_syntax():
    print("Testing frontend_e2e_test.py syntax...")
    try:
        import frontend_e2e_test
        print("✅ Syntax is valid!")
        return True
    except Exception as e:
        print(f"❌ Syntax error: {e}")
        return False

if __name__ == "__main__":
    print("=== QUICK DIAGNOSTIC TEST ===\n")
    
    # Test syntax
    syntax_ok = test_syntax()
    
    # Test backend
    backend_ok = test_backend()
    
    print(f"\nResults:")
    print(f"- Syntax: {'✅' if syntax_ok else '❌'}")
    print(f"- Backend: {'✅' if backend_ok else '❌'}")
    
    if syntax_ok and not backend_ok:
        print("\n💡 The syntax is fixed! Start the backend with:")
        print("   python main.py")
    elif syntax_ok and backend_ok:
        print("\n🎉 Everything looks good! You can now run the E2E test.")
