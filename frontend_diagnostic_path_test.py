#!/usr/bin/env python3
"""
Frontend Diagnostic Path Test
This test simulates the exact frontend interaction path to identify where phase progression fails.
It mimics the frontend "Start Lesson" button flow and compares it to the backend test path.
"""

import json
import time
import uuid
import requests
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
ENHANCE_CONTENT_URL = f"{BASE_URL}/api/enhance-content"

# Test data that mimics frontend request
TEST_SESSION_ID = f"frontend_test_{int(time.time())}"
TEST_STUDENT_ID = "test_student_123"
TEST_LESSON_REF = "angles_in_triangles"

def test_frontend_diagnostic_path():
    """Test the exact frontend diagnostic path to identify phase progression issues."""
    
    print("🔥 FRONTEND DIAGNOSTIC PATH TEST")
    print("=" * 60)
    print(f"Session ID: {TEST_SESSION_ID}")
    print(f"Student ID: {TEST_STUDENT_ID}")
    print(f"Lesson Ref: {TEST_LESSON_REF}")
    print()
    
    # Step 1: Initial lesson start (mimics frontend "Start Lesson" button)
    print("📝 STEP 1: Initial lesson start (Start Lesson button)")
    initial_request = {
        "session_id": TEST_SESSION_ID,
        "student_id": TEST_STUDENT_ID,
        "lesson_ref": TEST_LESSON_REF,
        "content_to_enhance": "Start lesson",  # This is what frontend sends
        "grade": "9",
        "subject": "Mathematics", 
        "level": "3",
        "student_info": {
            "first_name": "TestStudent",
            "country": "Nigeria"
        },
        "chat_history": []
    }
    
    try:
        print(f"🚀 Sending initial request...")
        response = requests.post(ENHANCE_CONTENT_URL, json=initial_request, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Initial request successful")
            print(f"📊 Response status: {result.get('status', 'Unknown')}")
            
            # Extract session state from response
            lesson_state = result.get('lesson_state', {})
            current_phase = lesson_state.get('current_phase', 'Unknown')
            print(f"🔍 Current phase after initial request: '{current_phase}'")
            
            # Check if we're stuck in diagnostic_start_probe
            if current_phase == 'diagnostic_start_probe':
                print("⚠️ DETECTED: Stuck in diagnostic_start_probe phase")
                print("🔧 Testing progression with student response...")
                
                # Step 2: Student response to try to trigger progression
                student_response_request = {
                    "session_id": TEST_SESSION_ID,
                    "student_id": TEST_STUDENT_ID,
                    "lesson_ref": TEST_LESSON_REF,
                    "content_to_enhance": "I'm ready to start",  # Student response
                    "grade": "9",
                    "subject": "Mathematics", 
                    "level": "3",
                    "student_info": {
                        "first_name": "TestStudent",
                        "country": "Nigeria"
                    },
                    "chat_history": [
                        {"role": "assistant", "content": result.get('response', '')},
                        {"role": "user", "content": "I'm ready to start"}
                    ]
                }
                
                print(f"🚀 Sending student response...")
                response2 = requests.post(ENHANCE_CONTENT_URL, json=student_response_request, timeout=60)
                
                if response2.status_code == 200:
                    result2 = response2.json()
                    lesson_state2 = result2.get('lesson_state', {})
                    current_phase2 = lesson_state2.get('current_phase', 'Unknown')
                    print(f"🔍 Current phase after student response: '{current_phase2}'")
                    
                    if current_phase2 != current_phase:
                        print(f"✅ PHASE PROGRESSION SUCCESSFUL: {current_phase} → {current_phase2}")
                    else:
                        print(f"❌ PHASE PROGRESSION FAILED: Still stuck in '{current_phase}'")
                        print("🔍 Detailed analysis needed...")
                        
                        # Print detailed response for analysis
                        print("\n📋 DETAILED RESPONSE ANALYSIS:")
                        print(f"Response content preview: {result2.get('response', 'No response')[:200]}...")
                        print(f"Lesson state keys: {list(lesson_state2.keys())}")
                        print(f"Session state: {lesson_state2}")
                        
                else:
                    print(f"❌ Student response request failed: {response2.status_code}")
                    print(f"Error: {response2.text}")
            
            elif current_phase and current_phase != 'diagnostic_start_probe':
                print(f"✅ Phase progression working: Current phase is '{current_phase}'")
            else:
                print(f"❓ Unexpected phase state: '{current_phase}'")
                
        else:
            print(f"❌ Initial request failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def compare_with_backend_test():
    """Compare the results with what the backend test path produces."""
    
    print("\n" + "=" * 60)
    print("🧪 BACKEND TEST PATH COMPARISON")
    print("=" * 60)
    
    # This would be the backend test approach (for comparison)
    print("📝 Backend test approach:")
    print("1. Calls calculate_next_mandatory_phase('diagnostic_start_probe')")
    print("2. Expected result: 'diagnostic_probing_L5_ask_q1'")
    print("3. Continues through all 8 diagnostic phases")
    print("4. Successfully completes lesson flow")
    
    print("\n📝 Frontend path issue:")
    print("1. Gets stuck in 'diagnostic_start_probe'")
    print("2. Doesn't progress to 'diagnostic_probing_L5_ask_q1'")
    print("3. Phase calculation logic not triggered or bypassed")

if __name__ == "__main__":
    # Make sure the backend is running
    try:
        health_check = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_check.status_code == 200:
            print("✅ Backend is running")
        else:
            print("⚠️ Backend health check failed")
    except:
        print("❌ Backend not accessible - make sure it's running on port 5000")
        exit(1)
    
    # Run the frontend diagnostic path test
    test_frontend_diagnostic_path()
    
    # Compare with backend test approach
    compare_with_backend_test()
    
    print(f"\n🏁 Frontend diagnostic path test completed at {datetime.now()}")
