import sys
import os

# Add the backend path to sys.path
backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
sys.path.insert(0, backend_path)

print(f"Backend path: {backend_path}")
print(f"Path exists: {os.path.exists(backend_path)}")

try:
    from main import calculate_next_mandatory_phase
    print("✓ Successfully imported calculate_next_mandatory_phase")
    
    # Test a simple call
    result = calculate_next_mandatory_phase("diagnostic_start_probe", "test_request_001")
    print(f"✓ Test call successful: diagnostic_start_probe -> {result}")
    
    # Test a few more
    result2 = calculate_next_mandatory_phase("teaching", "test_request_002")
    print(f"✓ Test call successful: teaching -> {result2}")
    
    result3 = calculate_next_mandatory_phase("completed", "test_request_003")
    print(f"✓ Test call successful: completed -> {result3}")
    
    print("\n🎉 Basic phase calculation is working!")
    
except ImportError as e:
    print(f"✗ Import error: {e}")
except Exception as e:
    print(f"✗ Runtime error: {e}")
    import traceback
    traceback.print_exc()
