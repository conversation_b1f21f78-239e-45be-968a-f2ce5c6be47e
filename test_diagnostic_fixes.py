#!/usr/bin/env python3
"""
Test script to verify the diagnostic phase progression fixes are working correctly.
"""

import sys
import os
import json
import re

# Add the backend directory to the path so we can import main
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager'))

def test_calculate_next_mandatory_phase():
    """Test the new calculate_next_mandatory_phase function"""
    print("🧪 Testing calculate_next_mandatory_phase function...")
    
    try:
        import main
        
        # Test cases for the 8-phase diagnostic progression
        test_cases = [
            ('diagnostic_start_probe', 'diagnostic_probing_L5_ask_q1'),
            ('diagnostic_probing_L5_ask_q1', 'diagnostic_probing_L5_eval_q1_ask_q2'),
            ('diagnostic_probing_L5_eval_q1_ask_q2', 'diagnostic_probing_L5_eval_q2_ask_q3'),
            ('diagnostic_probing_L5_eval_q2_ask_q3', 'diagnostic_probing_L5_eval_q3_ask_q4'),
            ('diagnostic_probing_L5_eval_q3_ask_q4', 'diagnostic_probing_L5_eval_q4_ask_q5'),
            ('diagnostic_probing_L5_eval_q4_ask_q5', 'diagnostic_probing_L5_eval_q5_decide_level'),
            ('diagnostic_probing_L5_eval_q5_decide_level', 'teaching_start_level_5'),
        ]
        
        all_passed = True
        for current_phase, expected_next in test_cases:
            result = main.calculate_next_mandatory_phase(current_phase, 'test-req')
            if result == expected_next:
                print(f"   ✅ {current_phase} → {result}")
            else:
                print(f"   ❌ {current_phase} → Expected: {expected_next}, Got: {result}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"   ❌ Error testing calculate_next_mandatory_phase: {e}")
        return False

def test_base_instructor_rules():
    """Test that BASE_INSTRUCTOR_RULES has been updated correctly"""
    print("🧪 Testing BASE_INSTRUCTOR_RULES updates...")
    
    try:
        import main
        
        rules = main.BASE_INSTRUCTOR_RULES
        
        checks = [
            ('python_calculated_new_phase_for_block placeholder', 'python_calculated_new_phase_for_block' in rules),
            ('MANDATORY STATE BLOCK instruction', 'MANDATORY STATE BLOCK' in rules),
            ('First Interaction ONLY', 'First Interaction ONLY' in rules),
            ('After ANY Student Response', 'After ANY Student Response' in rules),
            ('MANDATORY TRANSITION', 'MANDATORY TRANSITION' in rules),
        ]
        
        all_passed = True
        for check_name, check_result in checks:
            if check_result:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ Missing: {check_name}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"   ❌ Error testing BASE_INSTRUCTOR_RULES: {e}")
        return False

def test_ai_state_block_parsing():
    """Test the AI state block parsing logic"""
    print("🧪 Testing AI state block parsing...")
    
    try:
        # Test text with state block
        test_text = """Hello student! This is my response.

// AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_ask_q1", "current_question_index": 0} // AI_STATE_UPDATE_BLOCK_END"""
        
        # Test regex pattern
        state_block_match = re.search(
            r"//\s*AI_STATE_UPDATE_BLOCK_START\s*(\{.*?\})\s*//\s*AI_STATE_UPDATE_BLOCK_END",
            test_text, re.DOTALL
        )
        
        if state_block_match:
            state_json_str = state_block_match.group(1).strip()
            parsed_state = json.loads(state_json_str)
            
            if parsed_state.get('new_phase') == 'diagnostic_probing_L5_ask_q1':
                print("   ✅ AI state block parsing works correctly")
                return True
            else:
                print(f"   ❌ State block parsed incorrectly: {parsed_state}")
                return False
        else:
            print("   ❌ State block not found by regex")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing AI state block parsing: {e}")
        return False

def main_test():
    """Run all tests"""
    print("🚀 Running diagnostic phase progression fix verification tests...\n")
    
    results = {
        'calculate_next_mandatory_phase': test_calculate_next_mandatory_phase(),
        'base_instructor_rules': test_base_instructor_rules(),
        'ai_state_block_parsing': test_ai_state_block_parsing(),
    }
    
    print("\n📊 Test Results Summary:")
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED! Diagnostic phase progression fixes are working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    main_test()
