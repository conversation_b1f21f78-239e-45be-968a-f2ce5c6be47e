#!/usr/bin/env python3
"""
Direct Phase Calculation Test
This test directly calls the calculate_next_mandatory_phase function to verify it works correctly.
"""

import sys
import os

# Add the backend path to system path
backend_path = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
sys.path.insert(0, backend_path)

# Import the function from main.py
try:
    from main import calculate_next_mandatory_phase
    print("✅ Successfully imported calculate_next_mandatory_phase")
except ImportError as e:
    print(f"❌ Failed to import calculate_next_mandatory_phase: {e}")
    sys.exit(1)

def test_phase_calculation():
    """Test the calculate_next_mandatory_phase function directly."""
    
    print("🧪 DIRECT PHASE CALCULATION TEST")
    print("=" * 50)
    
    # Test cases that mimic the lesson flow
    test_cases = [
        ("diagnostic_start_probe", "diagnostic_probing_L5_ask_q1"),
        ("diagnostic_probing_L5_ask_q1", "diagnostic_probing_L5_eval_q1_ask_q2"),
        ("diagnostic_probing_L5_eval_q1_ask_q2", "diagnostic_probing_L5_eval_q2_ask_q3"),
        ("diagnostic_probing_L5_eval_q2_ask_q3", "diagnostic_probing_L5_eval_q3_ask_q4"),
        ("diagnostic_probing_L5_eval_q3_ask_q4", "diagnostic_probing_L5_eval_q4_ask_q5"),
        ("diagnostic_probing_L5_eval_q4_ask_q5", "diagnostic_probing_L5_eval_q5_decide_level"),
        ("diagnostic_probing_L5_eval_q5_decide_level", "teaching_start_level_5"),
        ("teaching_start_level_5", "teaching"),
        ("teaching", "quiz_initiate"),
        ("quiz_initiate", "quiz_questions"),
        ("quiz_questions", "quiz_results"),
        ("quiz_results", "conclusion_summary"),
        ("conclusion_summary", "final_assessment_pending"),
        ("final_assessment_pending", "completed"),
        ("completed", "completed")
    ]
    
    print(f"Testing {len(test_cases)} phase transitions...")
    print()
    
    all_passed = True
    for i, (current_phase, expected_next_phase) in enumerate(test_cases, 1):
        try:
            request_id = f"test_{i}"
            actual_next_phase = calculate_next_mandatory_phase(current_phase, request_id)
            
            if actual_next_phase == expected_next_phase:
                print(f"✅ Test {i:2d}: {current_phase} → {actual_next_phase}")
            else:
                print(f"❌ Test {i:2d}: {current_phase}")
                print(f"         Expected: {expected_next_phase}")
                print(f"         Actual:   {actual_next_phase}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Test {i:2d}: {current_phase} - ERROR: {e}")
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 ALL TESTS PASSED! The calculate_next_mandatory_phase function works correctly.")
        print("🔍 This means the issue is not in the phase calculation logic.")
        print("🔍 The issue must be in how the frontend API call processes the phase transition.")
    else:
        print("❌ Some tests failed. The phase calculation logic needs to be fixed.")
        
    return all_passed

def analyze_frontend_issue():
    """Analyze what might be causing the frontend issue."""
    
    print("\n" + "=" * 50)
    print("🔍 FRONTEND ISSUE ANALYSIS")
    print("=" * 50)
    
    print("If the phase calculation works correctly, the issue might be:")
    print()
    print("1. 🏗️  CONDITION LOGIC: The condition that triggers calculate_next_mandatory_phase")
    print("   may not be met for the frontend's initial request.")
    print()
    print("2. 📡 REQUEST FORMAT: The frontend might send a user_query that doesn't")
    print("   satisfy the progression conditions (lines 7417-7419 in main.py).")
    print()
    print("3. 💾 STATE PERSISTENCE: The calculated phase might not be properly")
    print("   persisted to Firestore or retrieved in subsequent requests.")
    print()
    print("4. 🤖 AI PROCESSING: The AI might not be generating the proper state")
    print("   update block with the calculated next phase.")
    print()
    print("RECOMMENDED FIXES:")
    print("- Add more debug logging to see exactly what user_query the frontend sends")
    print("- Verify the conditions in lines 7417-7419 are met")
    print("- Check that the AI state update block is properly generated and parsed")
    print("- Ensure Firestore persistence and retrieval works correctly")

if __name__ == "__main__":
    success = test_phase_calculation()
    analyze_frontend_issue()
    
    if success:
        print(f"\n🏁 Phase calculation test completed successfully!")
    else:
        print(f"\n💥 Phase calculation test failed - fix needed!")
