# EXACT LESSON FLOW CODE PATH - "START LESSON" TO "COMPLETED"

## Request Flow Summary

When a frontend makes a "start lesson" request, here's the **exact** code path through main.py:

## 1. Entry Point: `/api/enhance-content` (Line 5312)

```python
@app.route('/api/enhance-content', methods=['POST', 'OPTIONS'], endpoint='enhance_content_api')
@require_auth
@check_role_access
@performance_monitor  
@wrap_async
async def enhance_content_api(decoded_token):
```

**Request Structure**:
```json
{
    "student_id": "test_student",
    "lesson_ref": "P5-COM-001", 
    "grade": "Primary 5",
    "subject": "Computing",
    "level": "5",
    "session_id": "new_session_123",
    "content": "start lesson"
}
```

## 2. Session Initialization (Line 1573)

```python
def get_or_initialize_lesson_state(session_id_from_request: str, student_id: str, student_name: str, lesson_data_for_state: dict, is_new_session: bool = False) -> dict:
```

**Creates Initial State**:
```python
initial_state_dict = {
    "session_id": session_id_from_request,
    "student_id": student_id,
    "student_name": student_name,
    "current_lesson_phase": "diagnostic_start_probe",  # ALWAYS starts here
    "current_phase": "diagnostic_start_probe",         # Duplicate for consistency  
    "current_probing_level_number": 5,                 # Based on grade
    "current_question_index": 0,                       # Always start from 0
    "diagnostic_completed_this_session": False,
    # ... other initialization fields
}
```

## 3. Core Phase Calculation (Line 4097)

```python
def calculate_next_mandatory_phase(current_phase: str, request_id: str) -> str:
```

**Phase Progression Logic** (EXACT from main.py):

### A. Diagnostic Phases:
```python
if current_phase.startswith('diagnostic_'):
    # Extract the level number (e.g., '5' from '..._L5_...')
    level_match = re.search(r'_L(\d+)_', current_phase)
    level_num = level_match.group(1) if level_match else "5" # Default to 5 if not found

    # --- Mandatory 8-Phase Diagnostic Progression Rules ---
    if current_phase == 'diagnostic_start_probe':
        return f'diagnostic_probing_L{level_num}_ask_q1'
    
    # Handle intermediate evaluation/ask pairs (q1->q2, q2->q3, etc.)
    eval_match = re.search(r'_eval_q(\d+)_ask_q(\d+)', current_phase)
    if eval_match:
        ask_q_num = int(eval_match.group(2))
        if ask_q_num < 5:
            return f'diagnostic_probing_L{level_num}_eval_q{ask_q_num}_ask_q{ask_q_num + 1}'
        else:
            return f'diagnostic_probing_L{level_num}_eval_q5_decide_level'

    # Handle transition from first question to first evaluation
    if current_phase.endswith('_ask_q1'):
        return f'diagnostic_probing_L{level_num}_eval_q1_ask_q2'

    # Handle final decision step - transition to teaching
    if current_phase.endswith('_eval_q5_decide_level'):
        return f'teaching_start_level_{level_num}'
```

### B. Teaching Phases:
```python
elif current_phase.startswith('teaching_start_level_'):
    return 'teaching'
    
elif current_phase == 'teaching':
    return 'quiz_initiate'
```

### C. Quiz Phases:
```python
elif current_phase == 'quiz_initiate':
    return 'quiz_questions'
    
elif current_phase == 'quiz_questions':
    return 'quiz_results'
    
elif current_phase == 'quiz_results':
    return 'conclusion_summary'
```

### D. Completion Phases:
```python
elif current_phase == 'conclusion_summary':
    return 'final_assessment_pending'
    
elif current_phase == 'final_assessment_pending':
    return 'completed'
    
elif current_phase == 'completed':
    return 'completed'
```

## 4. AI Context Building (Lines 7420-7580)

For `diagnostic_start_probe` phase:
```python
python_calculated_new_phase_for_block = calculate_next_mandatory_phase(lesson_phase_from_context, request_id)
# Result: "diagnostic_probing_L5_ask_q1"

context_for_enhance = {
    'python_calculated_new_phase_for_block': python_calculated_new_phase_for_block,
    'lesson_phase': 'diagnostic_start_probe',
    'student_name': student_name,
    'topic': topic,
    'key_concepts_str': key_concepts_str,
    'current_probing_level_number': 5,
    # ... many other fields
}
```

## 5. AI Instruction Generation (Lines 2969-3200)

**BASE_INSTRUCTOR_RULES Template** (key excerpt):
```
MANDATORY: End with: // AI_STATE_UPDATE_BLOCK_START {"new_phase": "{python_calculated_new_phase_for_block}", "interaction_count": {interaction_count}} // AI_STATE_UPDATE_BLOCK_END

CRITICAL PHASE PROGRESSION: 
• The system has calculated the next phase as: {python_calculated_new_phase_for_block}
• You MUST use this exact phase name in your state update block

DIAGNOSTIC_START_PROBE SPECIFIC INSTRUCTIONS:
If the current phase is "diagnostic_start_probe":
- **MANDATORY TRANSITION**: You MUST transition to the next phase, which the system has calculated as **`{python_calculated_new_phase_for_block}`**.
- **MANDATORY STATE BLOCK**: 
  // AI_STATE_UPDATE_BLOCK_START {"new_phase": "{python_calculated_new_phase_for_block}", "current_probing_level_number": {current_probing_level_number}, "current_question_index": 0} // AI_STATE_UPDATE_BLOCK_END
```

## 6. AI Response (Line 10482)

**Generated Response Example**:
```
Hello Test Student! Welcome to our lesson on Introduction to Programming. I'm excited to help you learn about variables, functions, and loops. 

Before we begin the main lesson, I'd like to ask you a few questions to understand your current knowledge level. This will help me tailor the lesson perfectly for you.

Are you ready to start with the first diagnostic question?

// AI_STATE_UPDATE_BLOCK_START {"new_phase": "diagnostic_probing_L5_ask_q1", "current_probing_level_number": 5, "current_question_index": 0} // AI_STATE_UPDATE_BLOCK_END
```

## 7. State Block Parsing (Lines 6050-6200)

```python
# Parse AI state update block
pattern = r'// AI_STATE_UPDATE_BLOCK_START ({.*?}) // AI_STATE_UPDATE_BLOCK_END'
match = re.search(pattern, ai_response_text)

if match:
    state_json = match.group(1) 
    parsed_state = json.loads(state_json)
    # Result: {"new_phase": "diagnostic_probing_L5_ask_q1", "current_probing_level_number": 5, "current_question_index": 0}
```

## 8. Firestore State Update

```python
session_state_ref = db.collection(FS_COLLECTION_LESSON_STATES).document(session_id)
session_state_ref.set({
    'current_phase': 'diagnostic_probing_L5_ask_q1',
    'current_lesson_phase': 'diagnostic_probing_L5_ask_q1', 
    'current_probing_level_number': 5,
    'current_question_index': 0,
    'last_modified': SERVER_TIMESTAMP
}, merge=True)
```

## 9. Frontend Response

```json
{
    "success": true,
    "message": "Content enhanced successfully", 
    "data": {
        "enhanced_content": "Hello Test Student! Welcome to our lesson...",
        "current_phase": "diagnostic_probing_L5_ask_q1",
        "diagnostic_complete": false,
        "teaching_complete": false,
        "quiz_complete": false,
        "lesson_complete": false,
        "timing": {
            "total": 1250,
            "ai_processing": 890
        }
    }
}
```

---

## COMPLETE PHASE PROGRESSION SEQUENCE

Here's the **exact** progression based on the actual `calculate_next_mandatory_phase` logic:

| Current Phase | User Action | Next Phase | Code Line |
|---------------|-------------|------------|-----------|
| `diagnostic_start_probe` | "start lesson" | `diagnostic_probing_L5_ask_q1` | 4118 |
| `diagnostic_probing_L5_ask_q1` | answers Q1 | `diagnostic_probing_L5_eval_q1_ask_q2` | 4131 |
| `diagnostic_probing_L5_eval_q1_ask_q2` | answers Q2 | `diagnostic_probing_L5_eval_q2_ask_q3` | 4125 |
| `diagnostic_probing_L5_eval_q2_ask_q3` | answers Q3 | `diagnostic_probing_L5_eval_q3_ask_q4` | 4125 |
| `diagnostic_probing_L5_eval_q3_ask_q4` | answers Q4 | `diagnostic_probing_L5_eval_q4_ask_q5` | 4125 |
| `diagnostic_probing_L5_eval_q4_ask_q5` | answers Q5 | `diagnostic_probing_L5_eval_q5_decide_level` | 4127 |
| `diagnostic_probing_L5_eval_q5_decide_level` | assessment complete | `teaching_start_level_5` | 4134 |
| `teaching_start_level_5` | ready for teaching | `teaching` | 4140 |
| `teaching` | learning complete | `quiz_initiate` | 4143 |
| `quiz_initiate` | ready for quiz | `quiz_questions` | 4148 |
| `quiz_questions` | quiz complete | `quiz_results` | 4151 |
| `quiz_results` | results reviewed | `conclusion_summary` | 4154 |
| `conclusion_summary` | summary complete | `final_assessment_pending` | 4159 |
| `final_assessment_pending` | assessment done | `completed` | 4162 |
| `completed` | any action | `completed` | 4165 |

---

## Key Points for Frontend Comparison

1. **Initial Phase**: Every new session MUST start with `diagnostic_start_probe`
2. **Mandatory Progression**: Each phase has exactly ONE possible next phase (no skipping)
3. **State Enforcement**: Backend calculates next phase, AI must use it in state block
4. **Response Format**: AI response includes both content AND state update block
5. **Persistence**: Every transition is saved to Firestore `lesson_states` collection
6. **Frontend State**: Should match backend `current_phase` field exactly

**If your frontend testing shows different behavior, check:**
- Is the frontend starting with the correct initial phase?
- Are phase transitions happening one-by-one as expected?
- Is the frontend parsing the `current_phase` from the response correctly?
- Are there any phase skips or unexpected transitions?
- Does the frontend state match the backend Firestore state?
