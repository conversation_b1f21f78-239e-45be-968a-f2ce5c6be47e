#!/usr/bin/env python3
"""
Test script to check if main.py can be imported without errors
"""

import sys
import os

# Add the backend directory to the path
backend_dir = os.path.join(os.path.dirname(__file__), 'backend', 'cloud_function', 'lesson_manager')
sys.path.insert(0, backend_dir)

print("Testing main.py import...")
print(f"Backend directory: {backend_dir}")
print(f"Current working directory: {os.getcwd()}")

try:
    # Set environment variables that might be needed
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = os.path.join(backend_dir, 'solynta-academy-firebase-adminsdk-fbsvc-4569c5d419.json')
    os.environ['FLASK_ENV'] = 'development'
    os.environ['PORT'] = '5000'
    
    print("Environment variables set:")
    print(f"  GOOGLE_APPLICATION_CREDENTIALS: {os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')}")
    print(f"  FLASK_ENV: {os.environ.get('FLASK_ENV')}")
    print(f"  PORT: {os.environ.get('PORT')}")
    
    # Change to the backend directory
    original_cwd = os.getcwd()
    os.chdir(backend_dir)
    print(f"Changed to directory: {os.getcwd()}")
    
    # Try to import main
    print("Attempting to import main...")
    import main
    print("✅ Successfully imported main.py!")
    
    # Check if the Flask app exists
    if hasattr(main, 'app'):
        print("✅ Flask app found in main module")
        print(f"   App name: {main.app.name}")
        print(f"   Debug mode: {main.app.debug}")
        
        # List some routes
        print("   Sample routes:")
        for rule in list(main.app.url_map.iter_rules())[:5]:
            print(f"     {rule.rule} -> {rule.endpoint}")
    else:
        print("❌ No Flask app found in main module")
    
    # Restore original directory
    os.chdir(original_cwd)
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ Other error: {e}")
    import traceback
    traceback.print_exc()

print("Test completed.")
